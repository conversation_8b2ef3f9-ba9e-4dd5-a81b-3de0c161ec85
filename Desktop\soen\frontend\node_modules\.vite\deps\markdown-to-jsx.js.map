{"version": 3, "sources": ["../../markdown-to-jsx/index.tsx"], "sourcesContent": ["/* @jsx h */\n/**\n * markdown-to-jsx is a fork of\n * [simple-markdown v0.2.2](https://github.com/Khan/simple-markdown)\n * from Khan Academy. Thank you <PERSON> devs for making such an awesome\n * and extensible parsing infra... without it, half of the\n * optimizations here wouldn't be feasible. 🙏🏼\n */\nimport * as React from 'react'\n\n/**\n * Analogous to `node.type`. Please note that the values here may change at any time,\n * so do not hard code against the value directly.\n */\nexport const RuleType = {\n  blockQuote: '0',\n  breakLine: '1',\n  breakThematic: '2',\n  codeBlock: '3',\n  codeFenced: '4',\n  codeInline: '5',\n  footnote: '6',\n  footnoteReference: '7',\n  gfmTask: '8',\n  heading: '9',\n  headingSetext: '10',\n  /** only available if not `disableHTMLParsing` */\n  htmlBlock: '11',\n  htmlComment: '12',\n  /** only available if not `disableHTMLParsing` */\n  htmlSelfClosing: '13',\n  image: '14',\n  link: '15',\n  /** emits a `link` 'node', does not render directly */\n  linkAngleBraceStyleDetector: '16',\n  /** emits a `link` 'node', does not render directly */\n  linkBareUrlDetector: '17',\n  /** emits a `link` 'node', does not render directly */\n  linkMailtoDetector: '18',\n  newlineCoalescer: '19',\n  orderedList: '20',\n  paragraph: '21',\n  ref: '22',\n  refImage: '23',\n  refLink: '24',\n  table: '25',\n  tableSeparator: '26',\n  text: '27',\n  textBolded: '28',\n  textEmphasized: '29',\n  textEscaped: '30',\n  textMarked: '31',\n  textStrikethroughed: '32',\n  unorderedList: '33',\n} as const\n\nif (process.env.NODE_ENV !== 'production') {\n  Object.keys(RuleType).forEach(key => (RuleType[key] = key))\n}\n\nexport type RuleType = (typeof RuleType)[keyof typeof RuleType]\n\nconst enum Priority {\n  /**\n   * anything that must scan the tree before everything else\n   */\n  MAX,\n  /**\n   * scans for block-level constructs\n   */\n  HIGH,\n  /**\n   * inline w/ more priority than other inline\n   */\n  MED,\n  /**\n   * inline elements\n   */\n  LOW,\n  /**\n   * bare text and stuff that is considered leftovers\n   */\n  MIN,\n}\n\n/** TODO: Drop for React 16? */\nconst ATTRIBUTE_TO_JSX_PROP_MAP = [\n  'allowFullScreen',\n  'allowTransparency',\n  'autoComplete',\n  'autoFocus',\n  'autoPlay',\n  'cellPadding',\n  'cellSpacing',\n  'charSet',\n  'classId',\n  'colSpan',\n  'contentEditable',\n  'contextMenu',\n  'crossOrigin',\n  'encType',\n  'formAction',\n  'formEncType',\n  'formMethod',\n  'formNoValidate',\n  'formTarget',\n  'frameBorder',\n  'hrefLang',\n  'inputMode',\n  'keyParams',\n  'keyType',\n  'marginHeight',\n  'marginWidth',\n  'maxLength',\n  'mediaGroup',\n  'minLength',\n  'noValidate',\n  'radioGroup',\n  'readOnly',\n  'rowSpan',\n  'spellCheck',\n  'srcDoc',\n  'srcLang',\n  'srcSet',\n  'tabIndex',\n  'useMap',\n].reduce(\n  (obj, x) => {\n    obj[x.toLowerCase()] = x\n    return obj\n  },\n  { class: 'className', for: 'htmlFor' }\n)\n\nconst namedCodesToUnicode = {\n  amp: '\\u0026',\n  apos: '\\u0027',\n  gt: '\\u003e',\n  lt: '\\u003c',\n  nbsp: '\\u00a0',\n  quot: '\\u201c',\n} as const\n\nconst DO_NOT_PROCESS_HTML_ELEMENTS = ['style', 'script']\n\n/**\n * the attribute extractor regex looks for a valid attribute name,\n * followed by an equal sign (whitespace around the equal sign is allowed), followed\n * by one of the following:\n *\n * 1. a single quote-bounded string, e.g. 'foo'\n * 2. a double quote-bounded string, e.g. \"bar\"\n * 3. an interpolation, e.g. {something}\n *\n * JSX can be be interpolated into itself and is passed through the compiler using\n * the same options and setup as the current run.\n *\n * <Something children={<SomeOtherThing />} />\n *                      ==================\n *                              ↳ children: [<SomeOtherThing />]\n *\n * Otherwise, interpolations are handled as strings or simple booleans\n * unless HTML syntax is detected.\n *\n * <Something color={green} disabled={true} />\n *                   =====            ====\n *                     ↓                ↳ disabled: true\n *                     ↳ color: \"green\"\n *\n * Numbers are not parsed at this time due to complexities around int, float,\n * and the upcoming bigint functionality that would make handling it unwieldy.\n * Parse the string in your component as desired.\n *\n * <Something someBigNumber={123456789123456789} />\n *                           ==================\n *                                   ↳ someBigNumber: \"123456789123456789\"\n */\nconst ATTR_EXTRACTOR_R =\n  /([-A-Z0-9_:]+)(?:\\s*=\\s*(?:(?:\"((?:\\\\.|[^\"])*)\")|(?:'((?:\\\\.|[^'])*)')|(?:\\{((?:\\\\.|{[^}]*?}|[^}])*)\\})))?/gi\n\n/** TODO: Write explainers for each of these */\n\nconst AUTOLINK_MAILTO_CHECK_R = /mailto:/i\nconst BLOCK_END_R = /\\n{2,}$/\nconst BLOCKQUOTE_R = /^(\\s*>[\\s\\S]*?)(?=\\n\\n|$)/\nconst BLOCKQUOTE_TRIM_LEFT_MULTILINE_R = /^ *> ?/gm\nconst BLOCKQUOTE_ALERT_R = /^(?:\\[!([^\\]]*)\\]\\n)?([\\s\\S]*)/\nconst BREAK_LINE_R = /^ {2,}\\n/\nconst BREAK_THEMATIC_R = /^(?:( *[-*_])){3,} *(?:\\n *)+\\n/\nconst CODE_BLOCK_FENCED_R =\n  /^(?: {1,3})?(`{3,}|~{3,}) *(\\S+)? *([^\\n]*?)?\\n([\\s\\S]*?)(?:\\1\\n?|$)/\nconst CODE_BLOCK_R = /^(?: {4}[^\\n]+\\n*)+(?:\\n *)+\\n?/\nconst CODE_INLINE_R = /^(`+)((?:\\\\`|[^`])+)\\1/\nconst CONSECUTIVE_NEWLINE_R = /^(?:\\n *)*\\n/\nconst CR_NEWLINE_R = /\\r\\n?/g\n\n/**\n * Matches footnotes on the format:\n *\n * [^key]: value\n *\n * Matches multiline footnotes\n *\n * [^key]: row\n * row\n * row\n *\n * And empty lines in indented multiline footnotes\n *\n * [^key]: indented with\n *     row\n *\n *     row\n *\n * Explanation:\n *\n * 1. Look for the starting tag, eg: [^key]\n *    ^\\[\\^([^\\]]+)]\n *\n * 2. The first line starts with a colon, and continues for the rest of the line\n *   :(.*)\n *\n * 3. Parse as many additional lines as possible. Matches new non-empty lines that doesn't begin with a new footnote definition.\n *    (\\n(?!\\[\\^).+)\n *\n * 4. ...or allows for repeated newlines if the next line begins with at least four whitespaces.\n *    (\\n+ {4,}.*)\n */\nconst FOOTNOTE_R = /^\\[\\^([^\\]]+)](:(.*)((\\n+ {4,}.*)|(\\n(?!\\[\\^).+))*)/\n\nconst FOOTNOTE_REFERENCE_R = /^\\[\\^([^\\]]+)]/\nconst FORMFEED_R = /\\f/g\nconst FRONT_MATTER_R = /^---[ \\t]*\\n(.|\\n)*\\n---[ \\t]*\\n/\nconst GFM_TASK_R = /^\\s*?\\[(x|\\s)\\]/\nconst HEADING_R = /^ *(#{1,6}) *([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/\nconst HEADING_ATX_COMPLIANT_R =\n  /^ *(#{1,6}) +([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/\nconst HEADING_SETEXT_R = /^([^\\n]+)\\n *(=|-){3,} *(?:\\n *)+\\n/\n\n/**\n * Explanation:\n *\n * 1. Look for a starting tag, preceded by any amount of spaces\n *    ^ *<\n *\n * 2. Capture the tag name (capture 1)\n *    ([^ >/]+)\n *\n * 3. Ignore a space after the starting tag and capture the attribute portion of the tag (capture 2)\n *     ?([^>]*)>\n *\n * 4. Ensure a matching closing tag is present in the rest of the input string\n *    (?=[\\s\\S]*<\\/\\1>)\n *\n * 5. Capture everything until the matching closing tag -- this might include additional pairs\n *    of the same tag type found in step 2 (capture 3)\n *    ((?:[\\s\\S]*?(?:<\\1[^>]*>[\\s\\S]*?<\\/\\1>)*[\\s\\S]*?)*?)<\\/\\1>\n *\n * 6. Capture excess newlines afterward\n *    \\n*\n */\nconst HTML_BLOCK_ELEMENT_R =\n  /^ *(?!<[a-z][^ >/]* ?\\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\\n?(\\s*(?:<\\1[^>]*?>[\\s\\S]*?<\\/\\1>|(?!<\\1\\b)[\\s\\S])*?)<\\/\\1>(?!<\\/\\1>)\\n*/i\n\nconst HTML_CHAR_CODE_R = /&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi\n\nconst HTML_COMMENT_R = /^<!--[\\s\\S]*?(?:-->)/\n\n/**\n * borrowed from React 15(https://github.com/facebook/react/blob/894d20744cba99383ffd847dbd5b6e0800355a5c/src/renderers/dom/shared/HTMLDOMPropertyConfig.js)\n */\nconst HTML_CUSTOM_ATTR_R = /^(data|aria|x)-[a-z_][a-z\\d_.-]*$/\n\nconst HTML_SELF_CLOSING_ELEMENT_R =\n  /^ *<([a-z][a-z0-9:]*)(?:\\s+((?:<.*?>|[^>])*))?\\/?>(?!<\\/\\1>)(\\s*\\n)?/i\nconst INTERPOLATION_R = /^\\{.*\\}$/\nconst LINK_AUTOLINK_BARE_URL_R = /^(https?:\\/\\/[^\\s<]+[^<.,:;\"')\\]\\s])/\nconst LINK_AUTOLINK_MAILTO_R = /^<([^ >]+@[^ >]+)>/\nconst LINK_AUTOLINK_R = /^<([^ >]+:\\/[^ >]+)>/\nconst CAPTURE_LETTER_AFTER_HYPHEN = /-([a-z])?/gi\nconst NP_TABLE_R = /^(\\|.*)\\n(?: *(\\|? *[-:]+ *\\|[-| :]*)\\n((?:.*\\|.*\\n)*))?\\n?/\nconst PARAGRAPH_R = /^[^\\n]+(?:  \\n|\\n{2,})/\nconst REFERENCE_IMAGE_OR_LINK = /^\\[([^\\]]*)\\]:\\s+<?([^\\s>]+)>?\\s*(\"([^\"]*)\")?/\nconst REFERENCE_IMAGE_R = /^!\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/\nconst REFERENCE_LINK_R = /^\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/\nconst SHOULD_RENDER_AS_BLOCK_R = /(\\n|^[-*]\\s|^#|^ {2,}|^-{2,}|^>\\s)/\nconst TAB_R = /\\t/g\nconst TABLE_TRIM_PIPES = /(^ *\\||\\| *$)/g\nconst TABLE_CENTER_ALIGN = /^ *:-+: *$/\nconst TABLE_LEFT_ALIGN = /^ *:-+ *$/\nconst TABLE_RIGHT_ALIGN = /^ *-+: *$/\n\n/**\n * For inline formatting, this partial attempts to ignore characters that\n * may appear in nested formatting that could prematurely trigger detection\n * and therefore miss content that should have been included.\n */\nconst INLINE_SKIP_R =\n  '((?:\\\\[.*?\\\\][([].*?[)\\\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\\\\\\\1|[\\\\s\\\\S])+?)'\n\n/**\n * Detect a sequence like **foo** or __foo__. Note that bold has a higher priority\n * than emphasized to support nesting of both since they share a delimiter.\n */\nconst TEXT_BOLD_R = new RegExp(`^([*_])\\\\1${INLINE_SKIP_R}\\\\1\\\\1(?!\\\\1)`)\n\n/**\n * Detect a sequence like *foo* or _foo_.\n */\nconst TEXT_EMPHASIZED_R = new RegExp(`^([*_])${INLINE_SKIP_R}\\\\1(?!\\\\1)`)\n\n/**\n * Detect a sequence like ==foo==.\n */\nconst TEXT_MARKED_R = new RegExp(`^(==)${INLINE_SKIP_R}\\\\1`)\n\n/**\n * Detect a sequence like ~~foo~~.\n */\nconst TEXT_STRIKETHROUGHED_R = new RegExp(`^(~~)${INLINE_SKIP_R}\\\\1`)\n\nconst TEXT_ESCAPED_R = /^\\\\([^0-9A-Za-z\\s])/\nconst TEXT_UNESCAPE_R = /\\\\([^0-9A-Za-z\\s])/g\n\n/**\n * Always take the first character, then eagerly take text until a double space\n * (potential line break) or some markdown-like punctuation is reached.\n */\nconst TEXT_PLAIN_R = /^([\\s\\S](?:(?!  |[0-9]\\.)[^*_~\\-\\n<`\\\\\\[!])*)/\n\nconst TRIM_STARTING_NEWLINES = /^\\n+/\n\nconst HTML_LEFT_TRIM_AMOUNT_R = /^([ \\t]*)/\n\nconst UNESCAPE_URL_R = /\\\\([^\\\\])/g\n\ntype LIST_TYPE = 1 | 2\nconst ORDERED: LIST_TYPE = 1\nconst UNORDERED: LIST_TYPE = 2\n\nconst LIST_ITEM_END_R = / *\\n+$/\nconst LIST_LOOKBEHIND_R = /(?:^|\\n)( *)$/\n\n// recognize a `*` `-`, `+`, `1.`, `2.`... list bullet\nconst ORDERED_LIST_BULLET = '(?:\\\\d+\\\\.)'\nconst UNORDERED_LIST_BULLET = '(?:[*+-])'\n\nfunction generateListItemPrefix(type: LIST_TYPE) {\n  return (\n    '( *)(' +\n    (type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET) +\n    ') +'\n  )\n}\n\n// recognize the start of a list item:\n// leading space plus a bullet plus a space (`   * `)\nconst ORDERED_LIST_ITEM_PREFIX = generateListItemPrefix(ORDERED)\nconst UNORDERED_LIST_ITEM_PREFIX = generateListItemPrefix(UNORDERED)\n\nfunction generateListItemPrefixRegex(type: LIST_TYPE) {\n  return new RegExp(\n    '^' +\n      (type === ORDERED ? ORDERED_LIST_ITEM_PREFIX : UNORDERED_LIST_ITEM_PREFIX)\n  )\n}\n\nconst ORDERED_LIST_ITEM_PREFIX_R = generateListItemPrefixRegex(ORDERED)\nconst UNORDERED_LIST_ITEM_PREFIX_R = generateListItemPrefixRegex(UNORDERED)\n\nfunction generateListItemRegex(type: LIST_TYPE) {\n  // recognize an individual list item:\n  //  * hi\n  //    this is part of the same item\n  //\n  //    as is this, which is a new paragraph in the same item\n  //\n  //  * but this is not part of the same item\n  return new RegExp(\n    '^' +\n      (type === ORDERED\n        ? ORDERED_LIST_ITEM_PREFIX\n        : UNORDERED_LIST_ITEM_PREFIX) +\n      '[^\\\\n]*(?:\\\\n' +\n      '(?!\\\\1' +\n      (type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET) +\n      ' )[^\\\\n]*)*(\\\\n|$)',\n    'gm'\n  )\n}\n\nconst ORDERED_LIST_ITEM_R = generateListItemRegex(ORDERED)\nconst UNORDERED_LIST_ITEM_R = generateListItemRegex(UNORDERED)\n\n// check whether a list item has paragraphs: if it does,\n// we leave the newlines at the end\nfunction generateListRegex(type: LIST_TYPE) {\n  const bullet = type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET\n\n  return new RegExp(\n    '^( *)(' +\n      bullet +\n      ') ' +\n      '[\\\\s\\\\S]+?(?:\\\\n{2,}(?! )' +\n      '(?!\\\\1' +\n      bullet +\n      ' (?!' +\n      bullet +\n      ' ))\\\\n*' +\n      // the \\\\s*$ here is so that we can parse the inside of nested\n      // lists, where our content might end before we receive two `\\n`s\n      '|\\\\s*\\\\n*$)'\n  )\n}\n\nconst ORDERED_LIST_R = generateListRegex(ORDERED)\nconst UNORDERED_LIST_R = generateListRegex(UNORDERED)\n\nfunction generateListRule(\n  h: any,\n  type: LIST_TYPE\n): MarkdownToJSX.Rule<\n  MarkdownToJSX.OrderedListNode | MarkdownToJSX.UnorderedListNode\n> {\n  const ordered = type === ORDERED\n  const LIST_R = ordered ? ORDERED_LIST_R : UNORDERED_LIST_R\n  const LIST_ITEM_R = ordered ? ORDERED_LIST_ITEM_R : UNORDERED_LIST_ITEM_R\n  const LIST_ITEM_PREFIX_R = ordered\n    ? ORDERED_LIST_ITEM_PREFIX_R\n    : UNORDERED_LIST_ITEM_PREFIX_R\n\n  return {\n    match: allowInline(function (source, state) {\n      // We only want to break into a list if we are at the start of a\n      // line. This is to avoid parsing \"hi * there\" with \"* there\"\n      // becoming a part of a list.\n      // You might wonder, \"but that's inline, so of course it wouldn't\n      // start a list?\". You would be correct! Except that some of our\n      // lists can be inline, because they might be inside another list,\n      // in which case we can parse with inline scope, but need to allow\n      // nested lists inside this inline scope.\n      const isStartOfLine = LIST_LOOKBEHIND_R.exec(state.prevCapture)\n      const isListAllowed = state.list || (!state.inline && !state.simple)\n\n      if (isStartOfLine && isListAllowed) {\n        source = isStartOfLine[1] + source\n\n        return LIST_R.exec(source)\n      } else {\n        return null\n      }\n    }),\n    order: Priority.HIGH,\n    parse(capture, parse, state) {\n      const bullet = capture[2]\n      const start = ordered ? +bullet : undefined\n      const items = capture[0]\n        // recognize the end of a paragraph block inside a list item:\n        // two or more newlines at end end of the item\n        .replace(BLOCK_END_R, '\\n')\n        .match(LIST_ITEM_R)\n\n      let lastItemWasAParagraph = false\n\n      const itemContent = items.map(function (item, i) {\n        // We need to see how far indented the item is:\n        const space = LIST_ITEM_PREFIX_R.exec(item)[0].length\n\n        // And then we construct a regex to \"unindent\" the subsequent\n        // lines of the items by that amount:\n        const spaceRegex = new RegExp('^ {1,' + space + '}', 'gm')\n\n        // Before processing the item, we need a couple things\n        const content = item\n          // remove indents on trailing lines:\n          .replace(spaceRegex, '')\n          // remove the bullet:\n          .replace(LIST_ITEM_PREFIX_R, '')\n\n        // Handling \"loose\" lists, like:\n        //\n        //  * this is wrapped in a paragraph\n        //\n        //  * as is this\n        //\n        //  * as is this\n        const isLastItem = i === items.length - 1\n        const containsBlocks = content.indexOf('\\n\\n') !== -1\n\n        // Any element in a list is a block if it contains multiple\n        // newlines. The last element in the list can also be a block\n        // if the previous item in the list was a block (this is\n        // because non-last items in the list can end with \\n\\n, but\n        // the last item can't, so we just \"inherit\" this property\n        // from our previous element).\n        const thisItemIsAParagraph =\n          containsBlocks || (isLastItem && lastItemWasAParagraph)\n        lastItemWasAParagraph = thisItemIsAParagraph\n\n        // backup our state for delta afterwards. We're going to\n        // want to set state.list to true, and state.inline depending\n        // on our list's looseness.\n        const oldStateInline = state.inline\n        const oldStateList = state.list\n        state.list = true\n\n        // Parse inline if we're in a tight list, or block if we're in\n        // a loose list.\n        let adjustedContent\n        if (thisItemIsAParagraph) {\n          state.inline = false\n          adjustedContent = trimEnd(content) + '\\n\\n'\n        } else {\n          state.inline = true\n          adjustedContent = trimEnd(content)\n        }\n\n        const result = parse(adjustedContent, state)\n\n        // Restore our state before returning\n        state.inline = oldStateInline\n        state.list = oldStateList\n\n        return result\n      })\n\n      return {\n        items: itemContent,\n        ordered: ordered,\n        start: start,\n      }\n    },\n    render(node, output, state) {\n      const Tag = node.ordered ? 'ol' : 'ul'\n\n      return (\n        <Tag\n          key={state.key}\n          start={node.type === RuleType.orderedList ? node.start : undefined}\n        >\n          {node.items.map(function generateListItem(item, i) {\n            return <li key={i}>{output(item, state)}</li>\n          })}\n        </Tag>\n      )\n    },\n  }\n}\n\nconst LINK_INSIDE = '(?:\\\\[[^\\\\]]*\\\\]|[^\\\\[\\\\]]|\\\\](?=[^\\\\[]*\\\\]))*'\nconst LINK_HREF_AND_TITLE =\n  '\\\\s*<?((?:\\\\([^)]*\\\\)|[^\\\\s\\\\\\\\]|\\\\\\\\.)*?)>?(?:\\\\s+[\\'\"]([\\\\s\\\\S]*?)[\\'\"])?\\\\s*'\nconst LINK_R = new RegExp(\n  '^\\\\[(' + LINK_INSIDE + ')\\\\]\\\\(' + LINK_HREF_AND_TITLE + '\\\\)'\n)\nconst IMAGE_R = /^!\\[(.*?)\\]\\( *((?:\\([^)]*\\)|[^() ])*) *\"?([^)\"]*)?\"?\\)/\n\nconst NON_PARAGRAPH_BLOCK_SYNTAXES = [\n  BLOCKQUOTE_R,\n  CODE_BLOCK_FENCED_R,\n  CODE_BLOCK_R,\n  HEADING_R,\n  HEADING_SETEXT_R,\n  HEADING_ATX_COMPLIANT_R,\n  NP_TABLE_R,\n  ORDERED_LIST_R,\n  UNORDERED_LIST_R,\n]\n\nconst BLOCK_SYNTAXES = [\n  ...NON_PARAGRAPH_BLOCK_SYNTAXES,\n  PARAGRAPH_R,\n  HTML_BLOCK_ELEMENT_R,\n  HTML_COMMENT_R,\n  HTML_SELF_CLOSING_ELEMENT_R,\n]\n\nfunction trimEnd(str: string) {\n  let end = str.length\n  while (end > 0 && str[end - 1] <= ' ') end--\n  return str.slice(0, end)\n}\n\nfunction containsBlockSyntax(input: string) {\n  return BLOCK_SYNTAXES.some(r => r.test(input))\n}\n\n/** Remove symmetrical leading and trailing quotes */\nfunction unquote(str: string) {\n  const first = str[0]\n  if (\n    (first === '\"' || first === \"'\") &&\n    str.length >= 2 &&\n    str[str.length - 1] === first\n  ) {\n    return str.slice(1, -1)\n  }\n  return str\n}\n\n// based on https://stackoverflow.com/a/18123682/1141611\n// not complete, but probably good enough\nexport function slugify(str: string) {\n  return str\n    .replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g, 'a')\n    .replace(/[çÇ]/g, 'c')\n    .replace(/[ðÐ]/g, 'd')\n    .replace(/[ÈÉÊËéèêë]/g, 'e')\n    .replace(/[ÏïÎîÍíÌì]/g, 'i')\n    .replace(/[Ññ]/g, 'n')\n    .replace(/[øØœŒÕõÔôÓóÒò]/g, 'o')\n    .replace(/[ÜüÛûÚúÙù]/g, 'u')\n    .replace(/[ŸÿÝý]/g, 'y')\n    .replace(/[^a-z0-9- ]/gi, '')\n    .replace(/ /gi, '-')\n    .toLowerCase()\n}\n\nfunction parseTableAlignCapture(alignCapture: string) {\n  if (TABLE_RIGHT_ALIGN.test(alignCapture)) {\n    return 'right'\n  } else if (TABLE_CENTER_ALIGN.test(alignCapture)) {\n    return 'center'\n  } else if (TABLE_LEFT_ALIGN.test(alignCapture)) {\n    return 'left'\n  }\n\n  return null\n}\n\nfunction parseTableRow(\n  source: string,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State,\n  tableOutput: boolean\n): MarkdownToJSX.ParserResult[][] {\n  const prevInTable = state.inTable\n\n  state.inTable = true\n\n  let cells: MarkdownToJSX.ParserResult[][] = [[]]\n  let acc = ''\n\n  function flush() {\n    if (!acc) return\n\n    const cell = cells[cells.length - 1]\n    cell.push.apply(cell, parse(acc, state))\n    acc = ''\n  }\n\n  source\n    .trim()\n    // isolate situations where a pipe should be ignored (inline code, escaped, etc)\n    .split(/(`[^`]*`|\\\\\\||\\|)/)\n    .filter(Boolean)\n    .forEach((fragment, i, arr) => {\n      if (fragment.trim() === '|') {\n        flush()\n\n        if (tableOutput) {\n          if (i !== 0 && i !== arr.length - 1) {\n            // Split the current row\n            cells.push([])\n          }\n\n          return\n        }\n      }\n\n      acc += fragment\n    })\n\n  flush()\n\n  state.inTable = prevInTable\n\n  return cells\n}\n\nfunction parseTableAlign(source: string /*, parse, state*/) {\n  const alignText = source.replace(TABLE_TRIM_PIPES, '').split('|')\n\n  return alignText.map(parseTableAlignCapture)\n}\n\nfunction parseTableCells(\n  source: string,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State\n) {\n  const rowsText = source.trim().split('\\n')\n\n  return rowsText.map(function (rowText) {\n    return parseTableRow(rowText, parse, state, true)\n  })\n}\n\nfunction parseTable(\n  capture: RegExpMatchArray,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State\n) {\n  /**\n   * The table syntax makes some other parsing angry so as a bit of a hack even if alignment and/or cell rows are missing,\n   * we'll still run a detected first row through the parser and then just emit a paragraph.\n   */\n  state.inline = true\n  const align = capture[2] ? parseTableAlign(capture[2]) : []\n  const cells = capture[3] ? parseTableCells(capture[3], parse, state) : []\n  const header = parseTableRow(capture[1], parse, state, !!cells.length)\n  state.inline = false\n\n  return cells.length\n    ? {\n        align: align,\n        cells: cells,\n        header: header,\n        type: RuleType.table,\n      }\n    : {\n        children: header,\n        type: RuleType.paragraph,\n      }\n}\n\nfunction getTableStyle(node, colIndex) {\n  return node.align[colIndex] == null\n    ? {}\n    : {\n        textAlign: node.align[colIndex],\n      }\n}\n\n/** TODO: remove for react 16 */\nfunction normalizeAttributeKey(key) {\n  const hyphenIndex = key.indexOf('-')\n\n  if (hyphenIndex !== -1 && key.match(HTML_CUSTOM_ATTR_R) === null) {\n    key = key.replace(CAPTURE_LETTER_AFTER_HYPHEN, function (_, letter) {\n      return letter.toUpperCase()\n    })\n  }\n\n  return key\n}\n\nfunction attributeValueToJSXPropValue(\n  tag: MarkdownToJSX.HTMLTags,\n  key: keyof React.AllHTMLAttributes<Element>,\n  value: string,\n  sanitizeUrlFn: MarkdownToJSX.Options['sanitizer']\n): any {\n  if (key === 'style') {\n    return value.split(/;\\s?/).reduce(function (styles, kvPair) {\n      const key = kvPair.slice(0, kvPair.indexOf(':'))\n\n      // snake-case to camelCase\n      // also handles PascalCasing vendor prefixes\n      const camelCasedKey = key\n        .trim()\n        .replace(/(-[a-z])/g, substr => substr[1].toUpperCase())\n\n      // key.length + 1 to skip over the colon\n      styles[camelCasedKey] = kvPair.slice(key.length + 1).trim()\n\n      return styles\n    }, {})\n  } else if (key === 'href' || key === 'src') {\n    return sanitizeUrlFn(value, tag, key)\n  } else if (value.match(INTERPOLATION_R)) {\n    // return as a string and let the consumer decide what to do with it\n    value = value.slice(1, value.length - 1)\n  }\n\n  if (value === 'true') {\n    return true\n  } else if (value === 'false') {\n    return false\n  }\n\n  return value\n}\n\nfunction normalizeWhitespace(source: string): string {\n  return source\n    .replace(CR_NEWLINE_R, '\\n')\n    .replace(FORMFEED_R, '')\n    .replace(TAB_R, '    ')\n}\n\n/**\n * Creates a parser for a given set of rules, with the precedence\n * specified as a list of rules.\n *\n * @rules: an object containing\n * rule type -> {match, order, parse} objects\n * (lower order is higher precedence)\n * (Note: `order` is added to defaultRules after creation so that\n *  the `order` of defaultRules in the source matches the `order`\n *  of defaultRules in terms of `order` fields.)\n *\n * @returns The resulting parse function, with the following parameters:\n *   @source: the input source string to be parsed\n *   @state: an optional object to be threaded through parse\n *     calls. Allows clients to add stateful operations to\n *     parsing, such as keeping track of how many levels deep\n *     some nesting is. For an example use-case, see passage-ref\n *     parsing in src/widgets/passage/passage-markdown.jsx\n */\nfunction parserFor(\n  rules: MarkdownToJSX.Rules\n): (\n  source: string,\n  state: MarkdownToJSX.State\n) => ReturnType<MarkdownToJSX.NestedParser> {\n  // Sorts rules in order of increasing order, then\n  // ascending rule name in case of ties.\n  let ruleList = Object.keys(rules)\n\n  if (process.env.NODE_ENV !== 'production') {\n    ruleList.forEach(function (type) {\n      let order = rules[type].order\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        (typeof order !== 'number' || !isFinite(order))\n      ) {\n        console.warn(\n          'markdown-to-jsx: Invalid order for rule `' + type + '`: ' + order\n        )\n      }\n    })\n  }\n\n  ruleList.sort(function (typeA, typeB) {\n    let orderA = rules[typeA].order\n    let orderB = rules[typeB].order\n\n    // Sort based on increasing order\n    if (orderA !== orderB) {\n      return orderA - orderB\n    } else if (typeA < typeB) {\n      return -1\n    }\n\n    return 1\n  })\n\n  function nestedParse(\n    source: string,\n    state: MarkdownToJSX.State\n  ): MarkdownToJSX.ParserResult[] {\n    let result = []\n    let rule\n    let ruleType = ''\n    let parsed\n    let currCaptureString = ''\n\n    state.prevCapture = state.prevCapture || ''\n\n    // We store the previous capture so that match functions can\n    // use some limited amount of lookbehind. Lists use this to\n    // ensure they don't match arbitrary '- ' or '* ' in inline\n    // text (see the list rule for more information).\n    while (source) {\n      let i = 0\n      while (i < ruleList.length) {\n        ruleType = ruleList[i]\n        rule = rules[ruleType]\n\n        if (state.inline && !rule.match.inline) {\n          i++\n          continue\n        }\n\n        const capture = rule.match(source, state)\n\n        if (capture) {\n          currCaptureString = capture[0]\n\n          // retain what's been processed so far for lookbacks\n          state.prevCapture += currCaptureString\n\n          source = source.substring(currCaptureString.length)\n\n          parsed = rule.parse(capture, nestedParse, state)\n\n          // We also let rules override the default type of\n          // their parsed node if they would like to, so that\n          // there can be a single output function for all links,\n          // even if there are several rules to parse them.\n          if (parsed.type == null) {\n            parsed.type = ruleType as unknown as RuleType\n          }\n\n          result.push(parsed)\n          break\n        }\n\n        i++\n      }\n    }\n\n    // reset on exit\n    state.prevCapture = ''\n\n    return result\n  }\n\n  return function outerParse(source, state) {\n    return nestedParse(normalizeWhitespace(source), state)\n  }\n}\n\n/**\n * Marks a matcher function as eligible for being run inside an inline context;\n * allows us to do a little less work in the nested parser.\n */\nfunction allowInline<T extends Function & { inline?: 0 | 1 }>(fn: T) {\n  fn.inline = 1\n\n  return fn\n}\n\n// Creates a match function for an inline scoped or simple element from a regex\nfunction inlineRegex(regex: RegExp) {\n  return allowInline(function match(source, state: MarkdownToJSX.State) {\n    if (state.inline) {\n      return regex.exec(source)\n    } else {\n      return null\n    }\n  })\n}\n\n// basically any inline element except links\nfunction simpleInlineRegex(regex: RegExp) {\n  return allowInline(function match(\n    source: string,\n    state: MarkdownToJSX.State\n  ) {\n    if (state.inline || state.simple) {\n      return regex.exec(source)\n    } else {\n      return null\n    }\n  })\n}\n\n// Creates a match function for a block scoped element from a regex\nfunction blockRegex(regex: RegExp) {\n  return function match(source: string, state: MarkdownToJSX.State) {\n    if (state.inline || state.simple) {\n      return null\n    } else {\n      return regex.exec(source)\n    }\n  }\n}\n\n// Creates a match function from a regex, ignoring block/inline scope\nfunction anyScopeRegex(regex: RegExp) {\n  return allowInline(function match(source: string /*, state*/) {\n    return regex.exec(source)\n  })\n}\n\nfunction matchParagraph(source: string, state: MarkdownToJSX.State) {\n  if (state.inline || state.simple) {\n    return null\n  }\n\n  let match = ''\n\n  source.split('\\n').every(line => {\n    line += '\\n'\n\n    // bail out on first sign of non-paragraph block\n    if (NON_PARAGRAPH_BLOCK_SYNTAXES.some(regex => regex.test(line))) {\n      return false\n    }\n\n    match += line\n\n    return !!line.trim()\n  })\n\n  const captured = trimEnd(match)\n  if (captured == '') {\n    return null\n  }\n\n  // parseCaptureInline expects the inner content to be at index 2\n  // because index 1 is the delimiter for text formatting syntaxes\n  return [match, , captured]\n}\n\nexport function sanitizer(url: string): string {\n  try {\n    const decoded = decodeURIComponent(url).replace(/[^A-Za-z0-9/:]/g, '')\n\n    if (decoded.match(/^\\s*(javascript|vbscript|data(?!:image)):/i)) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          'Anchor URL contains an unsafe JavaScript/VBScript/data expression, it will not be rendered.',\n          decoded\n        )\n      }\n\n      return null\n    }\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(\n        'Anchor URL could not be decoded due to malformed syntax or characters, it will not be rendered.',\n        url\n      )\n    }\n\n    // decodeURIComponent sometimes throws a URIError\n    // See `decodeURIComponent('a%AFc');`\n    // http://stackoverflow.com/questions/9064536/javascript-decodeuricomponent-malformed-uri-exception\n    return null\n  }\n\n  return url\n}\n\nfunction unescapeUrl(rawUrlString: string): string {\n  return rawUrlString.replace(UNESCAPE_URL_R, '$1')\n}\n\n/**\n * Everything inline, including links.\n */\nfunction parseInline(\n  parse: MarkdownToJSX.NestedParser,\n  children: string,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  const isCurrentlySimple = state.simple || false\n  state.inline = true\n  state.simple = true\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  state.simple = isCurrentlySimple\n  return result\n}\n\n/**\n * Anything inline that isn't a link.\n */\nfunction parseSimpleInline(\n  parse: MarkdownToJSX.NestedParser,\n  children: string,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  const isCurrentlySimple = state.simple || false\n  state.inline = false\n  state.simple = true\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  state.simple = isCurrentlySimple\n  return result\n}\n\nfunction parseBlock(\n  parse,\n  children,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  state.inline = false\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  return result\n}\n\nconst parseCaptureInline: MarkdownToJSX.Parser<{\n  children: MarkdownToJSX.ParserResult[]\n}> = (capture, parse, state: MarkdownToJSX.State) => {\n  return {\n    children: parseInline(parse, capture[2], state),\n  }\n}\n\nfunction captureNothing() {\n  return {}\n}\n\nfunction renderNothing() {\n  return null\n}\n\nfunction reactFor(render) {\n  return function patchedRender(\n    ast: MarkdownToJSX.ParserResult | MarkdownToJSX.ParserResult[],\n    state: MarkdownToJSX.State = {}\n  ): React.ReactNode[] {\n    if (Array.isArray(ast)) {\n      const oldKey = state.key\n      const result = []\n\n      // map nestedOutput over the ast, except group any text\n      // nodes together into a single string output.\n      let lastWasString = false\n\n      for (let i = 0; i < ast.length; i++) {\n        state.key = i\n\n        const nodeOut = patchedRender(ast[i], state)\n        const isString = typeof nodeOut === 'string'\n\n        if (isString && lastWasString) {\n          result[result.length - 1] += nodeOut\n        } else if (nodeOut !== null) {\n          result.push(nodeOut)\n        }\n\n        lastWasString = isString\n      }\n\n      state.key = oldKey\n\n      return result\n    }\n\n    return render(ast, patchedRender, state)\n  }\n}\n\nfunction createRenderer(\n  rules: MarkdownToJSX.Rules,\n  userRender?: MarkdownToJSX.Options['renderRule']\n) {\n  return function renderRule(\n    ast: MarkdownToJSX.ParserResult,\n    render: MarkdownToJSX.RuleOutput,\n    state: MarkdownToJSX.State\n  ): React.ReactNode {\n    const renderer = rules[ast.type].render as MarkdownToJSX.Rule['render']\n\n    return userRender\n      ? userRender(() => renderer(ast, render, state), ast, render, state)\n      : renderer(ast, render, state)\n  }\n}\n\nfunction cx(...args) {\n  return args.filter(Boolean).join(' ')\n}\n\nfunction get(src: Object, path: string, fb?: any) {\n  let ptr = src\n  const frags = path.split('.')\n\n  while (frags.length) {\n    ptr = ptr[frags[0]]\n\n    if (ptr === undefined) break\n    else frags.shift()\n  }\n\n  return ptr || fb\n}\n\nfunction getTag(tag: string, overrides: MarkdownToJSX.Overrides) {\n  const override = get(overrides, tag)\n\n  if (!override) return tag\n\n  return typeof override === 'function' ||\n    (typeof override === 'object' && 'render' in override)\n    ? override\n    : get(overrides, `${tag}.component`, tag)\n}\n\nexport function compiler(\n  markdown: string = '',\n  options: MarkdownToJSX.Options = {}\n) {\n  options.overrides = options.overrides || {}\n  options.sanitizer = options.sanitizer || sanitizer\n  options.slugify = options.slugify || slugify\n  options.namedCodesToUnicode = options.namedCodesToUnicode\n    ? { ...namedCodesToUnicode, ...options.namedCodesToUnicode }\n    : namedCodesToUnicode\n\n  options.createElement = options.createElement || React.createElement\n\n  // JSX custom pragma\n  // eslint-disable-next-line no-unused-vars\n  function h(\n    // locally we always will render a known string tag\n    tag: MarkdownToJSX.HTMLTags,\n    props: Parameters<MarkdownToJSX.CreateElement>[1] & {\n      className?: string\n      id?: string\n    },\n    ...children\n  ) {\n    const overrideProps = get(options.overrides, `${tag}.props`, {})\n\n    return options.createElement(\n      getTag(tag, options.overrides),\n      {\n        ...props,\n        ...overrideProps,\n        className: cx(props?.className, overrideProps.className) || undefined,\n      },\n      ...children\n    )\n  }\n\n  function compile(input: string): React.JSX.Element {\n    input = input.replace(FRONT_MATTER_R, '')\n\n    let inline = false\n\n    if (options.forceInline) {\n      inline = true\n    } else if (!options.forceBlock) {\n      /**\n       * should not contain any block-level markdown like newlines, lists, headings,\n       * thematic breaks, blockquotes, tables, etc\n       */\n      inline = SHOULD_RENDER_AS_BLOCK_R.test(input) === false\n    }\n\n    const arr = emitter(\n      parser(\n        inline\n          ? input\n          : `${trimEnd(input).replace(TRIM_STARTING_NEWLINES, '')}\\n\\n`,\n        {\n          inline,\n        }\n      )\n    )\n\n    while (\n      typeof arr[arr.length - 1] === 'string' &&\n      !arr[arr.length - 1].trim()\n    ) {\n      arr.pop()\n    }\n\n    if (options.wrapper === null) {\n      return arr\n    }\n\n    const wrapper = options.wrapper || (inline ? 'span' : 'div')\n    let jsx\n\n    if (arr.length > 1 || options.forceWrapper) {\n      jsx = arr\n    } else if (arr.length === 1) {\n      jsx = arr[0]\n\n      // TODO: remove this for React 16\n      if (typeof jsx === 'string') {\n        return <span key=\"outer\">{jsx}</span>\n      } else {\n        return jsx\n      }\n    } else {\n      // TODO: return null for React 16\n      jsx = null\n    }\n\n    return options.createElement(\n      wrapper,\n      { key: 'outer' },\n      jsx\n    ) as React.JSX.Element\n  }\n\n  function attrStringToMap(\n    tag: MarkdownToJSX.HTMLTags,\n    str: string\n  ): React.JSX.IntrinsicAttributes {\n    const attributes = str.match(ATTR_EXTRACTOR_R)\n    if (!attributes) {\n      return null\n    }\n\n    return attributes.reduce(function (map, raw) {\n      const delimiterIdx = raw.indexOf('=')\n\n      if (delimiterIdx !== -1) {\n        const key = normalizeAttributeKey(raw.slice(0, delimiterIdx)).trim()\n        const value = unquote(raw.slice(delimiterIdx + 1).trim())\n\n        const mappedKey = ATTRIBUTE_TO_JSX_PROP_MAP[key] || key\n\n        // bail out, not supported\n        if (mappedKey === 'ref') return map\n\n        const normalizedValue = (map[mappedKey] = attributeValueToJSXPropValue(\n          tag,\n          key,\n          value,\n          options.sanitizer\n        ))\n\n        if (\n          typeof normalizedValue === 'string' &&\n          (HTML_BLOCK_ELEMENT_R.test(normalizedValue) ||\n            HTML_SELF_CLOSING_ELEMENT_R.test(normalizedValue))\n        ) {\n          map[mappedKey] = compile(normalizedValue.trim())\n        }\n      } else if (raw !== 'style') {\n        map[ATTRIBUTE_TO_JSX_PROP_MAP[raw] || raw] = true\n      }\n\n      return map\n    }, {})\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof markdown !== 'string') {\n      throw new Error(`markdown-to-jsx: the first argument must be\n                             a string`)\n    }\n\n    if (\n      Object.prototype.toString.call(options.overrides) !== '[object Object]'\n    ) {\n      throw new Error(`markdown-to-jsx: options.overrides (second argument property) must be\n                             undefined or an object literal with shape:\n                             {\n                                htmltagname: {\n                                    component: string|ReactComponent(optional),\n                                    props: object(optional)\n                                }\n                             }`)\n    }\n  }\n\n  const footnotes: { footnote: string; identifier: string }[] = []\n  const refs: { [key: string]: { target: string; title: string } } = {}\n\n  /**\n   * each rule's react() output function goes through our custom\n   * h() JSX pragma; this allows the override functionality to be\n   * automatically applied\n   */\n  // @ts-ignore\n  const rules: MarkdownToJSX.Rules = {\n    [RuleType.blockQuote]: {\n      match: blockRegex(BLOCKQUOTE_R),\n      order: Priority.HIGH,\n      parse(capture, parse, state) {\n        const [, alert, content] = capture[0]\n          .replace(BLOCKQUOTE_TRIM_LEFT_MULTILINE_R, '')\n          .match(BLOCKQUOTE_ALERT_R)\n\n        return {\n          alert,\n          children: parse(content, state),\n        }\n      },\n      render(node, output, state) {\n        const props = {\n          key: state.key,\n        } as Record<string, unknown>\n\n        if (node.alert) {\n          props.className =\n            'markdown-alert-' +\n            options.slugify(node.alert.toLowerCase(), slugify)\n\n          node.children.unshift({\n            attrs: {},\n            children: [{ type: RuleType.text, text: node.alert }],\n            noInnerParse: true,\n            type: RuleType.htmlBlock,\n            tag: 'header',\n          })\n        }\n\n        return h('blockquote', props, output(node.children, state))\n      },\n    },\n\n    [RuleType.breakLine]: {\n      match: anyScopeRegex(BREAK_LINE_R),\n      order: Priority.HIGH,\n      parse: captureNothing,\n      render(_, __, state) {\n        return <br key={state.key} />\n      },\n    },\n\n    [RuleType.breakThematic]: {\n      match: blockRegex(BREAK_THEMATIC_R),\n      order: Priority.HIGH,\n      parse: captureNothing,\n      render(_, __, state) {\n        return <hr key={state.key} />\n      },\n    },\n\n    [RuleType.codeBlock]: {\n      match: blockRegex(CODE_BLOCK_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          lang: undefined,\n          text: trimEnd(capture[0].replace(/^ {4}/gm, '')).replace(\n            TEXT_UNESCAPE_R,\n            '$1'\n          ),\n        }\n      },\n\n      render(node, output, state) {\n        return (\n          <pre key={state.key}>\n            <code\n              {...node.attrs}\n              className={node.lang ? `lang-${node.lang}` : ''}\n            >\n              {node.text}\n            </code>\n          </pre>\n        )\n      },\n    } as MarkdownToJSX.Rule<{\n      attrs?: ReturnType<typeof attrStringToMap>\n      lang?: string\n      text: string\n    }>,\n\n    [RuleType.codeFenced]: {\n      match: blockRegex(CODE_BLOCK_FENCED_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          // if capture[3] it's additional metadata\n          attrs: attrStringToMap('code', capture[3] || ''),\n          lang: capture[2] || undefined,\n          text: capture[4].replace(TEXT_UNESCAPE_R, '$1'),\n          type: RuleType.codeBlock,\n        }\n      },\n    },\n\n    [RuleType.codeInline]: {\n      match: simpleInlineRegex(CODE_INLINE_R),\n      order: Priority.LOW,\n      parse(capture /*, parse, state*/) {\n        return {\n          text: capture[2].replace(TEXT_UNESCAPE_R, '$1'),\n        }\n      },\n      render(node, output, state) {\n        return <code key={state.key}>{node.text}</code>\n      },\n    },\n\n    /**\n     * footnotes are emitted at the end of compilation in a special <footer> block\n     */\n    [RuleType.footnote]: {\n      match: blockRegex(FOOTNOTE_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        footnotes.push({\n          footnote: capture[2],\n          identifier: capture[1],\n        })\n\n        return {}\n      },\n      render: renderNothing,\n    },\n\n    [RuleType.footnoteReference]: {\n      match: inlineRegex(FOOTNOTE_REFERENCE_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse*/) {\n        return {\n          target: `#${options.slugify(capture[1], slugify)}`,\n          text: capture[1],\n        }\n      },\n      render(node, output, state) {\n        return (\n          <a key={state.key} href={options.sanitizer(node.target, 'a', 'href')}>\n            <sup key={state.key}>{node.text}</sup>\n          </a>\n        )\n      },\n    } as MarkdownToJSX.Rule<{ target: string; text: string }>,\n\n    [RuleType.gfmTask]: {\n      match: inlineRegex(GFM_TASK_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        return {\n          completed: capture[1].toLowerCase() === 'x',\n        }\n      },\n      render(node, output, state) {\n        return (\n          <input\n            checked={node.completed}\n            key={state.key}\n            readOnly\n            type=\"checkbox\"\n          />\n        )\n      },\n    } as MarkdownToJSX.Rule<{ completed: boolean }>,\n\n    [RuleType.heading]: {\n      match: blockRegex(\n        options.enforceAtxHeadings ? HEADING_ATX_COMPLIANT_R : HEADING_R\n      ),\n      order: Priority.HIGH,\n      parse(capture, parse, state) {\n        return {\n          children: parseInline(parse, capture[2], state),\n          id: options.slugify(capture[2], slugify),\n          level: capture[1].length as MarkdownToJSX.HeadingNode['level'],\n        }\n      },\n      render(node, output, state) {\n        return h(\n          `h${node.level}`,\n          { id: node.id, key: state.key },\n          output(node.children, state)\n        )\n      },\n    },\n\n    [RuleType.headingSetext]: {\n      match: blockRegex(HEADING_SETEXT_R),\n      order: Priority.MAX,\n      parse(capture, parse, state) {\n        return {\n          children: parseInline(parse, capture[1], state),\n          level: capture[2] === '=' ? 1 : 2,\n          type: RuleType.heading,\n        }\n      },\n    },\n\n    [RuleType.htmlBlock]: {\n      /**\n       * find the first matching end tag and process the interior\n       */\n      match: anyScopeRegex(HTML_BLOCK_ELEMENT_R),\n      order: Priority.HIGH,\n      parse(capture, parse, state) {\n        const [, whitespace] = capture[3].match(HTML_LEFT_TRIM_AMOUNT_R)\n\n        const trimmer = new RegExp(`^${whitespace}`, 'gm')\n        const trimmed = capture[3].replace(trimmer, '')\n\n        const parseFunc = containsBlockSyntax(trimmed)\n          ? parseBlock\n          : parseInline\n\n        const tagName = capture[1].toLowerCase() as MarkdownToJSX.HTMLTags\n        const noInnerParse =\n          DO_NOT_PROCESS_HTML_ELEMENTS.indexOf(tagName) !== -1\n\n        const tag = (\n          noInnerParse ? tagName : capture[1]\n        ).trim() as MarkdownToJSX.HTMLTags\n\n        const ast = {\n          attrs: attrStringToMap(tag, capture[2]),\n          noInnerParse: noInnerParse,\n          tag,\n        } as {\n          attrs: ReturnType<typeof attrStringToMap>\n          children?: ReturnType<MarkdownToJSX.NestedParser> | undefined\n          noInnerParse: Boolean\n          tag: MarkdownToJSX.HTMLTags\n          text?: string | undefined\n        }\n\n        state.inAnchor = state.inAnchor || tagName === 'a'\n\n        if (noInnerParse) {\n          ast.text = capture[3]\n        } else {\n          ast.children = parseFunc(parse, trimmed, state)\n        }\n\n        /**\n         * if another html block is detected within, parse as block,\n         * otherwise parse as inline to pick up any further markdown\n         */\n        state.inAnchor = false\n\n        return ast\n      },\n      render(node, output, state) {\n        return (\n          <node.tag key={state.key} {...node.attrs}>\n            {node.text || (node.children ? output(node.children, state) : '')}\n          </node.tag>\n        )\n      },\n    },\n\n    [RuleType.htmlSelfClosing]: {\n      /**\n       * find the first matching end tag and process the interior\n       */\n      match: anyScopeRegex(HTML_SELF_CLOSING_ELEMENT_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        const tag = capture[1].trim() as MarkdownToJSX.HTMLTags\n\n        return {\n          attrs: attrStringToMap(tag, capture[2] || ''),\n          tag,\n        }\n      },\n      render(node, output, state) {\n        return <node.tag {...node.attrs} key={state.key} />\n      },\n    },\n\n    [RuleType.htmlComment]: {\n      match: anyScopeRegex(HTML_COMMENT_R),\n      order: Priority.HIGH,\n      parse() {\n        return {}\n      },\n      render: renderNothing,\n    },\n\n    [RuleType.image]: {\n      match: simpleInlineRegex(IMAGE_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        return {\n          alt: capture[1],\n          target: unescapeUrl(capture[2]),\n          title: capture[3],\n        }\n      },\n      render(node, output, state) {\n        return (\n          <img\n            key={state.key}\n            alt={node.alt || undefined}\n            title={node.title || undefined}\n            src={options.sanitizer(node.target, 'img', 'src')}\n          />\n        )\n      },\n    } as MarkdownToJSX.Rule<{\n      alt?: string\n      target: string\n      title?: string\n    }>,\n\n    [RuleType.link]: {\n      match: inlineRegex(LINK_R),\n      order: Priority.LOW,\n      parse(capture, parse, state) {\n        return {\n          children: parseSimpleInline(parse, capture[1], state),\n          target: unescapeUrl(capture[2]),\n          title: capture[3],\n        }\n      },\n      render(node, output, state) {\n        return (\n          <a\n            key={state.key}\n            href={options.sanitizer(node.target, 'a', 'href')}\n            title={node.title}\n          >\n            {output(node.children, state)}\n          </a>\n        )\n      },\n    },\n\n    // https://daringfireball.net/projects/markdown/syntax#autolink\n    [RuleType.linkAngleBraceStyleDetector]: {\n      match: inlineRegex(LINK_AUTOLINK_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          children: [\n            {\n              text: capture[1],\n              type: RuleType.text,\n            },\n          ],\n          target: capture[1],\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.linkBareUrlDetector]: {\n      match: allowInline((source, state) => {\n        if (state.inAnchor || options.disableAutoLink) {\n          return null\n        }\n\n        return inlineRegex(LINK_AUTOLINK_BARE_URL_R)(source, state)\n      }),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          children: [\n            {\n              text: capture[1],\n              type: RuleType.text,\n            },\n          ],\n          target: capture[1],\n          title: undefined,\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.linkMailtoDetector]: {\n      match: inlineRegex(LINK_AUTOLINK_MAILTO_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        let address = capture[1]\n        let target = capture[1]\n\n        // Check for a `mailto:` already existing in the link:\n        if (!AUTOLINK_MAILTO_CHECK_R.test(target)) {\n          target = 'mailto:' + target\n        }\n\n        return {\n          children: [\n            {\n              text: address.replace('mailto:', ''),\n              type: RuleType.text,\n            },\n          ],\n          target: target,\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.orderedList]: generateListRule(\n      h,\n      ORDERED\n    ) as MarkdownToJSX.Rule<MarkdownToJSX.OrderedListNode>,\n\n    [RuleType.unorderedList]: generateListRule(\n      h,\n      UNORDERED\n    ) as MarkdownToJSX.Rule<MarkdownToJSX.UnorderedListNode>,\n\n    [RuleType.newlineCoalescer]: {\n      match: blockRegex(CONSECUTIVE_NEWLINE_R),\n      order: Priority.LOW,\n      parse: captureNothing,\n      render(/*node, output, state*/) {\n        return '\\n'\n      },\n    },\n\n    [RuleType.paragraph]: {\n      match: allowInline(matchParagraph),\n      order: Priority.LOW,\n      parse: parseCaptureInline,\n      render(node, output, state) {\n        return <p key={state.key}>{output(node.children, state)}</p>\n      },\n    } as MarkdownToJSX.Rule<ReturnType<typeof parseCaptureInline>>,\n\n    [RuleType.ref]: {\n      match: inlineRegex(REFERENCE_IMAGE_OR_LINK),\n      order: Priority.MAX,\n      parse(capture /*, parse*/) {\n        refs[capture[1]] = {\n          target: capture[2],\n          title: capture[4],\n        }\n\n        return {}\n      },\n      render: renderNothing,\n    },\n\n    [RuleType.refImage]: {\n      match: simpleInlineRegex(REFERENCE_IMAGE_R),\n      order: Priority.MAX,\n      parse(capture) {\n        return {\n          alt: capture[1] || undefined,\n          ref: capture[2],\n        }\n      },\n      render(node, output, state) {\n        return refs[node.ref] ? (\n          <img\n            key={state.key}\n            alt={node.alt}\n            src={options.sanitizer(refs[node.ref].target, 'img', 'src')}\n            title={refs[node.ref].title}\n          />\n        ) : null\n      },\n    } as MarkdownToJSX.Rule<{ alt?: string; ref: string }>,\n\n    [RuleType.refLink]: {\n      match: inlineRegex(REFERENCE_LINK_R),\n      order: Priority.MAX,\n      parse(capture, parse, state) {\n        return {\n          children: parse(capture[1], state),\n          fallbackChildren: capture[0],\n          ref: capture[2],\n        }\n      },\n      render(node, output, state) {\n        return refs[node.ref] ? (\n          <a\n            key={state.key}\n            href={options.sanitizer(refs[node.ref].target, 'a', 'href')}\n            title={refs[node.ref].title}\n          >\n            {output(node.children, state)}\n          </a>\n        ) : (\n          <span key={state.key}>{node.fallbackChildren}</span>\n        )\n      },\n    },\n\n    [RuleType.table]: {\n      match: blockRegex(NP_TABLE_R),\n      order: Priority.HIGH,\n      parse: parseTable,\n      render(node, output, state) {\n        const table = node as MarkdownToJSX.TableNode\n        return (\n          <table key={state.key}>\n            <thead>\n              <tr>\n                {table.header.map(function generateHeaderCell(content, i) {\n                  return (\n                    <th key={i} style={getTableStyle(table, i)}>\n                      {output(content, state)}\n                    </th>\n                  )\n                })}\n              </tr>\n            </thead>\n\n            <tbody>\n              {table.cells.map(function generateTableRow(row, i) {\n                return (\n                  <tr key={i}>\n                    {row.map(function generateTableCell(content, c) {\n                      return (\n                        <td key={c} style={getTableStyle(table, c)}>\n                          {output(content, state)}\n                        </td>\n                      )\n                    })}\n                  </tr>\n                )\n              })}\n            </tbody>\n          </table>\n        )\n      },\n    },\n\n    [RuleType.text]: {\n      // Here we look for anything followed by non-symbols,\n      // double newlines, or double-space-newlines\n      // We break on any symbol characters so that this grammar\n      // is easy to extend without needing to modify this regex\n      match: anyScopeRegex(TEXT_PLAIN_R),\n      order: Priority.MIN,\n      parse(capture /*, parse, state*/) {\n        return {\n          text: capture[0]\n            // nbsp -> unicode equivalent for named chars\n            .replace(HTML_CHAR_CODE_R, (full, inner) => {\n              return options.namedCodesToUnicode[inner]\n                ? options.namedCodesToUnicode[inner]\n                : full\n            }),\n        }\n      },\n      render(node /*, output, state*/) {\n        return node.text\n      },\n    },\n\n    [RuleType.textBolded]: {\n      match: simpleInlineRegex(TEXT_BOLD_R),\n      order: Priority.MED,\n      parse(capture, parse, state) {\n        return {\n          // capture[1] -> the syntax control character\n          // capture[2] -> inner content\n          children: parse(capture[2], state),\n        }\n      },\n      render(node, output, state) {\n        return <strong key={state.key}>{output(node.children, state)}</strong>\n      },\n    },\n\n    [RuleType.textEmphasized]: {\n      match: simpleInlineRegex(TEXT_EMPHASIZED_R),\n      order: Priority.LOW,\n      parse(capture, parse, state) {\n        return {\n          // capture[1] -> opening * or _\n          // capture[2] -> inner content\n          children: parse(capture[2], state),\n        }\n      },\n      render(node, output, state) {\n        return <em key={state.key}>{output(node.children, state)}</em>\n      },\n    },\n\n    [RuleType.textEscaped]: {\n      // We don't allow escaping numbers, letters, or spaces here so that\n      // backslashes used in plain text still get rendered. But allowing\n      // escaping anything else provides a very flexible escape mechanism,\n      // regardless of how this grammar is extended.\n      match: simpleInlineRegex(TEXT_ESCAPED_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        return {\n          text: capture[1],\n          type: RuleType.text,\n        }\n      },\n    },\n\n    [RuleType.textMarked]: {\n      match: simpleInlineRegex(TEXT_MARKED_R),\n      order: Priority.LOW,\n      parse: parseCaptureInline,\n      render(node, output, state) {\n        return <mark key={state.key}>{output(node.children, state)}</mark>\n      },\n    },\n\n    [RuleType.textStrikethroughed]: {\n      match: simpleInlineRegex(TEXT_STRIKETHROUGHED_R),\n      order: Priority.LOW,\n      parse: parseCaptureInline,\n      render(node, output, state) {\n        return <del key={state.key}>{output(node.children, state)}</del>\n      },\n    },\n  }\n\n  // Object.keys(rules).forEach(key => {\n  //   let { match: match, parse: parse } = rules[key]\n\n  //   // rules[key].match = (...args) => {\n  //   //   const start = performance.now()\n  //   //   const result = match(...args)\n  //   //   const delta = performance.now() - start\n\n  //   //   if (delta > 5)\n  //   //     console.warn(\n  //   //       `Slow match for ${key}: ${delta.toFixed(3)}ms, input: ${args[0]}`\n  //   //     )\n\n  //   //   return result\n  //   // }\n\n  //   rules[key].parse = (...args) => {\n  //     const start = performance.now()\n  //     const result = parse(...args)\n  //     const delta = performance.now() - start\n\n  //     console[delta > 5 ? 'warn' : 'log'](\n  //       `${key}:parse`,\n  //       `${delta.toFixed(3)}ms`,\n  //       args[0]\n  //     )\n\n  //     return result\n  //   }\n  // })\n\n  if (options.disableParsingRawHTML === true) {\n    delete rules[RuleType.htmlBlock]\n    delete rules[RuleType.htmlSelfClosing]\n  }\n\n  const parser = parserFor(rules)\n  const emitter: Function = reactFor(createRenderer(rules, options.renderRule))\n\n  const jsx = compile(markdown)\n\n  if (footnotes.length) {\n    return (\n      <div>\n        {jsx}\n        <footer key=\"footer\">\n          {footnotes.map(function createFootnote(def) {\n            return (\n              <div\n                id={options.slugify(def.identifier, slugify)}\n                key={def.identifier}\n              >\n                {def.identifier}\n                {emitter(parser(def.footnote, { inline: true }))}\n              </div>\n            )\n          })}\n        </footer>\n      </div>\n    )\n  }\n\n  return jsx\n}\n\n/**\n * A simple HOC for easy React use. Feed the markdown content as a direct child\n * and the rest is taken care of automatically.\n */\nconst Markdown: React.FC<\n  Omit<React.HTMLAttributes<Element>, 'children'> & {\n    children: string\n    options?: MarkdownToJSX.Options\n  }\n> = ({ children = '', options, ...props }) => {\n  if (process.env.NODE_ENV !== 'production' && typeof children !== 'string') {\n    console.error(\n      'markdown-to-jsx: <Markdown> component only accepts a single string as a child, received:',\n      children\n    )\n  }\n\n  return React.cloneElement(\n    compiler(children, options),\n    props as React.JSX.IntrinsicAttributes\n  )\n}\n\nexport namespace MarkdownToJSX {\n  /**\n   * RequireAtLeastOne<{ ... }> <- only requires at least one key\n   */\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>\n    }[Keys]\n\n  export type CreateElement = typeof React.createElement\n\n  export type HTMLTags = keyof React.JSX.IntrinsicElements\n\n  export type State = {\n    /** true if the current content is inside anchor link grammar */\n    inAnchor?: boolean\n    /** true if parsing in an inline context (subset of rules around formatting and links) */\n    inline?: boolean\n    /** true if in a table */\n    inTable?: boolean\n    /** use this for the `key` prop */\n    key?: React.Key\n    /** true if in a list */\n    list?: boolean\n    /** used for lookbacks */\n    prevCapture?: string\n    /** true if parsing in inline context w/o links */\n    simple?: boolean\n  }\n\n  export interface BlockQuoteNode {\n    alert?: string\n    children: MarkdownToJSX.ParserResult[]\n    type: typeof RuleType.blockQuote\n  }\n\n  export interface BreakLineNode {\n    type: typeof RuleType.breakLine\n  }\n\n  export interface BreakThematicNode {\n    type: typeof RuleType.breakThematic\n  }\n\n  export interface CodeBlockNode {\n    type: typeof RuleType.codeBlock\n    attrs?: React.JSX.IntrinsicAttributes\n    lang?: string\n    text: string\n  }\n\n  export interface CodeFencedNode {\n    type: typeof RuleType.codeFenced\n  }\n\n  export interface CodeInlineNode {\n    type: typeof RuleType.codeInline\n    text: string\n  }\n\n  export interface FootnoteNode {\n    type: typeof RuleType.footnote\n  }\n\n  export interface FootnoteReferenceNode {\n    type: typeof RuleType.footnoteReference\n    target: string\n    text: string\n  }\n\n  export interface GFMTaskNode {\n    type: typeof RuleType.gfmTask\n    completed: boolean\n  }\n\n  export interface HeadingNode {\n    type: typeof RuleType.heading\n    children: MarkdownToJSX.ParserResult[]\n    id: string\n    level: 1 | 2 | 3 | 4 | 5 | 6\n  }\n\n  export interface HeadingSetextNode {\n    type: typeof RuleType.headingSetext\n  }\n\n  export interface HTMLCommentNode {\n    type: typeof RuleType.htmlComment\n  }\n\n  export interface ImageNode {\n    type: typeof RuleType.image\n    alt?: string\n    target: string\n    title?: string\n  }\n\n  export interface LinkNode {\n    type: typeof RuleType.link\n    children: MarkdownToJSX.ParserResult[]\n    target: string\n    title?: string\n  }\n\n  export interface LinkAngleBraceNode {\n    type: typeof RuleType.linkAngleBraceStyleDetector\n  }\n\n  export interface LinkBareURLNode {\n    type: typeof RuleType.linkBareUrlDetector\n  }\n\n  export interface LinkMailtoNode {\n    type: typeof RuleType.linkMailtoDetector\n  }\n\n  export interface OrderedListNode {\n    type: typeof RuleType.orderedList\n    items: MarkdownToJSX.ParserResult[][]\n    ordered: true\n    start?: number\n  }\n\n  export interface UnorderedListNode {\n    type: typeof RuleType.unorderedList\n    items: MarkdownToJSX.ParserResult[][]\n    ordered: false\n  }\n\n  export interface NewlineNode {\n    type: typeof RuleType.newlineCoalescer\n  }\n\n  export interface ParagraphNode {\n    type: typeof RuleType.paragraph\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface ReferenceNode {\n    type: typeof RuleType.ref\n  }\n\n  export interface ReferenceImageNode {\n    type: typeof RuleType.refImage\n    alt?: string\n    ref: string\n  }\n\n  export interface ReferenceLinkNode {\n    type: typeof RuleType.refLink\n    children: MarkdownToJSX.ParserResult[]\n    fallbackChildren: string\n    ref: string\n  }\n\n  export interface TableNode {\n    type: typeof RuleType.table\n    /**\n     * alignment for each table column\n     */\n    align: ('left' | 'right' | 'center')[]\n    cells: MarkdownToJSX.ParserResult[][][]\n    header: MarkdownToJSX.ParserResult[][]\n  }\n\n  export interface TableSeparatorNode {\n    type: typeof RuleType.tableSeparator\n  }\n\n  export interface TextNode {\n    type: typeof RuleType.text\n    text: string\n  }\n\n  export interface BoldTextNode {\n    type: typeof RuleType.textBolded\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface ItalicTextNode {\n    type: typeof RuleType.textEmphasized\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface EscapedTextNode {\n    type: typeof RuleType.textEscaped\n  }\n\n  export interface MarkedTextNode {\n    type: typeof RuleType.textMarked\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface StrikethroughTextNode {\n    type: typeof RuleType.textStrikethroughed\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface HTMLNode {\n    type: typeof RuleType.htmlBlock\n    attrs: React.JSX.IntrinsicAttributes\n    children?: ReturnType<MarkdownToJSX.NestedParser> | undefined\n    noInnerParse: Boolean\n    tag: MarkdownToJSX.HTMLTags\n    text?: string | undefined\n  }\n\n  export interface HTMLSelfClosingNode {\n    type: typeof RuleType.htmlSelfClosing\n    attrs: React.JSX.IntrinsicAttributes\n    tag: string\n  }\n\n  export type ParserResult =\n    | BlockQuoteNode\n    | BreakLineNode\n    | BreakThematicNode\n    | CodeBlockNode\n    | CodeFencedNode\n    | CodeInlineNode\n    | FootnoteNode\n    | FootnoteReferenceNode\n    | GFMTaskNode\n    | HeadingNode\n    | HeadingSetextNode\n    | HTMLCommentNode\n    | ImageNode\n    | LinkNode\n    | LinkAngleBraceNode\n    | LinkBareURLNode\n    | LinkMailtoNode\n    | OrderedListNode\n    | UnorderedListNode\n    | NewlineNode\n    | ParagraphNode\n    | ReferenceNode\n    | ReferenceImageNode\n    | ReferenceLinkNode\n    | TableNode\n    | TableSeparatorNode\n    | TextNode\n    | BoldTextNode\n    | ItalicTextNode\n    | EscapedTextNode\n    | MarkedTextNode\n    | StrikethroughTextNode\n    | HTMLNode\n    | HTMLSelfClosingNode\n\n  export type NestedParser = (\n    input: string,\n    state?: MarkdownToJSX.State\n  ) => MarkdownToJSX.ParserResult[]\n\n  export type Parser<ParserOutput> = (\n    capture: RegExpMatchArray,\n    nestedParse: NestedParser,\n    state?: MarkdownToJSX.State\n  ) => ParserOutput\n\n  export type RuleOutput = (\n    ast: MarkdownToJSX.ParserResult | MarkdownToJSX.ParserResult[],\n    state: MarkdownToJSX.State\n  ) => React.JSX.Element\n\n  export type Rule<ParserOutput = MarkdownToJSX.ParserResult> = {\n    match: (\n      source: string,\n      state: MarkdownToJSX.State,\n      prevCapturedString?: string\n    ) => RegExpMatchArray\n    order: Priority\n    parse: MarkdownToJSX.Parser<Omit<ParserOutput, 'type'>>\n    render?: (\n      node: ParserOutput,\n      /**\n       * Continue rendering AST nodes if applicable.\n       */\n      render: RuleOutput,\n      state?: MarkdownToJSX.State\n    ) => React.ReactNode\n  }\n\n  export type Rules = {\n    [K in ParserResult['type']]: K extends typeof RuleType.table\n      ? Rule<Extract<ParserResult, { type: K | typeof RuleType.paragraph }>>\n      : Rule<Extract<ParserResult, { type: K }>>\n  }\n\n  export type Override =\n    | RequireAtLeastOne<{\n        component: React.ElementType\n        props: Object\n      }>\n    | React.ElementType\n\n  export type Overrides = {\n    [tag in HTMLTags]?: Override\n  } & {\n    [customComponent: string]: Override\n  }\n\n  export type Options = Partial<{\n    /**\n     * Ultimate control over the output of all rendered JSX.\n     */\n    createElement: (\n      tag: Parameters<CreateElement>[0],\n      props: React.JSX.IntrinsicAttributes,\n      ...children: React.ReactNode[]\n    ) => React.ReactNode\n\n    /**\n     * The library automatically generates an anchor tag for bare URLs included in the markdown\n     * document, but this behavior can be disabled if desired.\n     */\n    disableAutoLink: boolean\n\n    /**\n     * Disable the compiler's best-effort transcription of provided raw HTML\n     * into JSX-equivalent. This is the functionality that prevents the need to\n     * use `dangerouslySetInnerHTML` in React.\n     */\n    disableParsingRawHTML: boolean\n\n    /**\n     * Forces the compiler to have space between hash sign and the header text which\n     * is explicitly stated in the most of the markdown specs.\n     * https://github.github.com/gfm/#atx-heading\n     * `The opening sequence of # characters must be followed by a space or by the end of line.`\n     */\n    enforceAtxHeadings: boolean\n\n    /**\n     * Forces the compiler to always output content with a block-level wrapper\n     * (`<p>` or any block-level syntax your markdown already contains.)\n     */\n    forceBlock: boolean\n\n    /**\n     * Forces the compiler to always output content with an inline wrapper (`<span>`)\n     */\n    forceInline: boolean\n\n    /**\n     * Forces the compiler to wrap results, even if there is only a single\n     * child or no children.\n     */\n    forceWrapper: boolean\n\n    /**\n     * Supply additional HTML entity: unicode replacement mappings.\n     *\n     * Pass only the inner part of the entity as the key,\n     * e.g. `&le;` -> `{ \"le\": \"\\u2264\" }`\n     *\n     * By default\n     * the following entities are replaced with their unicode equivalents:\n     *\n     * ```\n     * &amp;\n     * &apos;\n     * &gt;\n     * &lt;\n     * &nbsp;\n     * &quot;\n     * ```\n     */\n    namedCodesToUnicode: {\n      [key: string]: string\n    }\n\n    /**\n     * Selectively control the output of particular HTML tags as they would be\n     * emitted by the compiler.\n     */\n    overrides: Overrides\n\n    /**\n     * Allows for full control over rendering of particular rules.\n     * For example, to implement a LaTeX renderer such as `react-katex`:\n     *\n     * ```\n     * renderRule(next, node, renderChildren, state) {\n     *   if (node.type === RuleType.codeBlock && node.lang === 'latex') {\n     *     return (\n     *       <TeX as=\"div\" key={state.key}>\n     *         {String.raw`${node.text}`}\n     *       </TeX>\n     *     )\n     *   }\n     *\n     *   return next();\n     * }\n     * ```\n     *\n     * Thar be dragons obviously, but you can do a lot with this\n     * (have fun!) To see how things work internally, check the `render`\n     * method in source for a particular rule.\n     */\n    renderRule: (\n      /** Resume normal processing, call this function as a fallback if you are not returning custom JSX. */\n      next: () => React.ReactNode,\n      /** the current AST node, use `RuleType` against `node.type` for identification */\n      node: ParserResult,\n      /** use as `renderChildren(node.children)` for block nodes */\n      renderChildren: RuleOutput,\n      /** contains `key` which should be supplied to the topmost JSX element */\n      state: State\n    ) => React.ReactNode\n\n    /**\n     * Override the built-in sanitizer function for URLs, etc if desired. The built-in version is available as a library export called `sanitizer`.\n     */\n    sanitizer: (\n      value: string,\n      tag: HTMLTags,\n      attribute: string\n    ) => string | null\n\n    /**\n     * Override normalization of non-URI-safe characters for use in generating\n     * HTML IDs for anchor linking purposes.\n     */\n    slugify: (input: string, defaultFn: (input: string) => string) => string\n\n    /**\n     * Declare the type of the wrapper to be used when there are multiple\n     * children to render. Set to `null` to get an array of children back\n     * without any wrapper, or use `React.Fragment` to get a React element\n     * that won't show up in the DOM.\n     */\n    wrapper: React.ElementType | null\n  }>\n}\n\nexport default Markdown\n"], "mappings": ";;;;;;;;;;;;;;;;;;;IAcaA,IAAW,EACtBC,YAAY,KACZC,WAAW,KACXC,eAAe,KACfC,WAAW,KACXC,YAAY,KACZC,YAAY,KACZC,UAAU,KACVC,mBAAmB,KACnBC,SAAS,KACTC,SAAS,KACTC,eAAe,MAEfC,WAAW,MACXC,aAAa,MAEbC,iBAAiB,MACjBC,OAAO,MACPC,MAAM,MAENC,6BAA6B,MAE7BC,qBAAqB,MAErBC,oBAAoB,MACpBC,kBAAkB,MAClBC,aAAa,MACbC,WAAW,MACXC,KAAK,MACLC,UAAU,MACVC,SAAS,MACTC,OAAO,MACPC,gBAAgB,MAChBC,MAAM,MACNC,YAAY,MACZC,gBAAgB,MAChBC,aAAa,MACbC,YAAY,MACZC,qBAAqB,MACrBC,eAAe,KAAA;AASjB,IAAWC;AAAAA,CAAX,SAAWA,IAAAA;AAITA,EAAAA,GAAAA,GAAAA,MAAAA,CAAAA,IAAAA,OAIAA,GAAAA,GAAAA,OAAAA,CAAAA,IAAAA,QAIAA,GAAAA,GAAAA,MAAAA,CAAAA,IAAAA,OAIAA,GAAAA,GAAAA,MAAAA,CAAAA,IAAAA,OAIAA,GAAAA,GAAAA,MAAAA,CAAAA,IAAAA;AACD,EArBUA,MAAAA,IAAAA,CAAAA,EAAAA;AAwBX,IAAMC,IAA4B,CAChC,mBACA,qBACA,gBACA,aACA,YACA,eACA,eACA,WACA,WACA,WACA,mBACA,eACA,eACA,WACA,cACA,eACA,cACA,kBACA,cACA,eACA,YACA,aACA,aACA,WACA,gBACA,eACA,aACA,cACA,aACA,cACA,cACA,YACA,WACA,cACA,UACA,WACA,UACA,YACA,QAAA,EACAC,OACA,CAACC,IAAKC,QACJD,GAAIC,GAAEC,YAAAA,CAAAA,IAAiBD,IAChBD,KAET,EAAEG,OAAO,aAAaC,KAAK,UAAA,CAAA;AA7C7B,IAgDMC,IAAsB,EAC1BC,KAAK,KACLC,MAAM,KACNC,IAAI,KACJC,IAAI,KACJC,MAAM,KACNC,MAAM,IAAA;AAtDR,IAyDMC,IAA+B,CAAC,SAAS,QAAA;AAzD/C,IA2FMC,IACJ;AA5FF,IAgGMC,IAA0B;AAhGhC,IAiGMC,IAAc;AAjGpB,IAkGMC,IAAe;AAlGrB,IAmGMC,IAAmC;AAnGzC,IAoGMC,IAAqB;AApG3B,IAqGMC,IAAe;AArGrB,IAsGMC,IAAmB;AAtGzB,IAuGMC,IACJ;AAxGF,IAyGMC,IAAe;AAzGrB,IA0GMC,IAAgB;AA1GtB,IA2GMC,IAAwB;AA3G9B,IA4GMC,IAAe;AA5GrB,IA8IMC,IAAa;AA9InB,IAgJMC,IAAuB;AAhJ7B,IAiJMC,IAAa;AAjJnB,IAkJMC,IAAiB;AAlJvB,IAmJMC,IAAa;AAnJnB,IAoJMC,IAAY;AApJlB,IAqJMC,IACJ;AAtJF,IAuJMC,IAAmB;AAvJzB,IA+KMC,IACJ;AAhLF,IAkLMC,IAAmB;AAlLzB,IAoLMC,IAAiB;AApLvB,IAyLMC,IAAqB;AAzL3B,IA2LMC,IACJ;AA5LF,IA6LMC,IAAkB;AA7LxB,IA8LMC,IAA2B;AA9LjC,IA+LMC,IAAyB;AA/L/B,IAgMMC,IAAkB;AAhMxB,IAiMMC,IAA8B;AAjMpC,IAkMMC,IAAa;AAlMnB,IAoMMC,IAA0B;AApMhC,IAqMMC,IAAoB;AArM1B,IAsMMC,IAAmB;AAtMzB,IAuMMC,IAA2B;AAvMjC,IAwMMC,IAAQ;AAxMd,IAyMMC,IAAmB;AAzMzB,IA0MMC,IAAqB;AA1M3B,IA2MMC,IAAmB;AA3MzB,IA4MMC,IAAoB;AA5M1B,IAmNMC,IACJ;AApNF,IA0NMC,IAAc,IAAIC,OAAAA,aAAoBF,CAAAA,eAAAA;AA1N5C,IA+NMG,IAAoB,IAAID,OAAAA,UAAiBF,CAAAA,YAAAA;AA/N/C,IAoOMI,IAAgB,IAAIF,OAAAA,QAAeF,CAAAA,KAAAA;AApOzC,IAyOMK,IAAyB,IAAIH,OAAAA,QAAeF,CAAAA,KAAAA;AAzOlD,IA2OMM,KAAiB;AA3OvB,IA4OMC,KAAkB;AA5OxB,IAkPMC,KAAe;AAlPrB,IAoPMC,KAAyB;AApP/B,IAsPMC,KAA0B;AAtPhC,IAwPMC,KAAiB;AAxPvB,IA+PMC,KAAoB;AA/P1B,IAkQMC,KAAsB;AAlQ5B,IAmQMC,KAAwB;AAE9B,SAASC,GAAuBC,IAAAA;AAC9B,SACE,WAZuB,MAatBA,KAAmBH,KAAsBC,MAC1C;AAEJ;AAIA,IAAMG,KAA2BF,GApBN,CAAA;AAoB3B,IACMG,KAA6BH,GApBN,CAAA;AAsB7B,SAASI,GAA4BH,IAAAA;AACnC,SAAA,IAAWd,OACT,OAzBuB,MA0BpBc,KAAmBC,KAA2BC,GAAAA;AAErD;AAEA,IAAME,KAA6BD,GA9BR,CAAA;AA8B3B,IACME,KAA+BF,GA9BR,CAAA;AAgC7B,SAASG,GAAsBN,IAAAA;AAQ7B,SAAA,IAAWd,OACT,OA1CuB,MA2CpBc,KACGC,KACAC,MAHN,yBA1CuB,MAgDpBF,KAAmBH,KAAsBC,MAC1C,sBACF,IAAA;AAEJ;AAEA,IAAMS,KAAsBD,GAtDD,CAAA;AAsD3B,IACME,KAAwBF,GAtDD,CAAA;AA0D7B,SAASG,GAAkBT,IAAAA;AACzB,QAAMU,KA5DmB,MA4DVV,KAAmBH,KAAsBC;AAExD,SAAA,IAAWZ,OACT,WACEwB,KADF,sCAKEA,KACA,SACAA,KAPF,oBAAA;AAaJ;AAEA,IAAMC,KAAiBF,GA9EI,CAAA;AA8E3B,IACMG,KAAmBH,GA9EI,CAAA;AAgF7B,SAASI,GACPC,IACAd,IAAAA;AAIA,QAAMe,KAvFmB,MAuFTf,IACVgB,KAASD,KAAUJ,KAAiBC,IACpCK,KAAcF,KAAUR,KAAsBC,IAC9CU,KAAqBH,KACvBX,KACAC;AAEJ,SAAO,EACLc,OAAOC,GAAY,SAAUC,IAAQC,IAAAA;AASnC,UAAMC,KAAgB3B,GAAkB4B,KAAKF,GAAMG,WAAAA;AAGnD,WAAIF,OAFkBD,GAAMI,QAAAA,CAAUJ,GAAMK,UAAAA,CAAWL,GAAMM,UAKpDZ,GAAOQ,KAFdH,KAASE,GAAc,CAAA,IAAKF,EAAAA,IAAAA;EAMhC,CAAA,GACAQ,OAAAA,GACAC,MAAMC,IAASD,IAAOR,IAAAA;AACpB,UACMU,KAAQjB,KAAAA,CADCgB,GAAQ,CAAA,IAAA,QAEjBE,KAAQF,GAAQ,CAAA,EAGnBG,QAAQzF,GAAa,IAAA,EACrB0E,MAAMF,EAAAA;AAET,QAAIkB,KAAAA;AAgEJ,WAAO,EACLF,OA/DkBA,GAAMG,IAAI,SAAUC,IAAMC,IAAAA;AAE5C,YAAMC,KAAQrB,GAAmBM,KAAKa,EAAAA,EAAM,CAAA,EAAGG,QAIzCC,KAAa,IAAIvD,OAAO,UAAUqD,KAAQ,KAAK,IAAA,GAG/CG,KAAUL,GAEbH,QAAQO,IAAY,EAAA,EAEpBP,QAAQhB,IAAoB,EAAA,GASzByB,KAAaL,OAAML,GAAMO,SAAS,GASlCI,KAAAA,OARiBF,GAAQG,QAAQ,MAAA,KASlBF,MAAcR;AACnCA,MAAAA,KAAwBS;AAKxB,YAAME,KAAiBxB,GAAMK,QACvBoB,KAAezB,GAAMI;AAK3B,UAAIsB;AAJJ1B,MAAAA,GAAMI,OAAAA,MAKFkB,MACFtB,GAAMK,SAAAA,OACNqB,KAAkBC,GAAQP,EAAAA,IAAW,WAErCpB,GAAMK,SAAAA,MACNqB,KAAkBC,GAAQP,EAAAA;AAG5B,YAAMQ,KAASpB,GAAMkB,IAAiB1B,EAAAA;AAMtC,aAHAA,GAAMK,SAASmB,IACfxB,GAAMI,OAAOqB,IAENG;IACT,CAAA,GAIEnC,SAASA,IACTiB,OAAOA,GAAAA;EAEX,GACAmB,QAAMA,CAACC,IAAMC,IAAQ/B,OAIjBR,GAHUsC,GAAKrC,UAAU,OAAO,MAAA,EAI9BuC,KAAKhC,GAAMgC,KACXtB,OAAOoB,GAAKpD,SAAS5G,EAASqB,cAAc2I,GAAKpB,QAAAA,OAAQuB,GAExDH,GAAKnB,MAAMG,IAAI,SAA0BC,IAAMC,IAAAA;AAC9C,WAAOxB,GAAAA,MAAAA,EAAIwC,KAAKhB,GAAAA,GAAIe,GAAOhB,IAAMf,EAAAA,CAAAA;EACnC,CAAA,CAAA,EAAA;AAKV;AAEA,IAGMkC,KAAS,IAAItE,OACjB,4IAAA;AAJF,IAMMuE,KAAU;AANhB,IAQMC,KAA+B,CACnChH,GACAK,GACAC,GACAS,GACAE,GACAD,GACAY,GACAqC,IACAC,EAAAA;AAjBF,IAoBM+C,KAAiB,CAAA,GAClBD,IAjSe,0BAmSlB9F,GACAE,GACAE,CAAAA;AAGF,SAASiF,GAAQW,IAAAA;AACf,MAAIC,KAAMD,GAAIpB;AACd,SAAOqB,KAAM,KAAKD,GAAIC,KAAM,CAAA,KAAM,MAAKA,CAAAA;AACvC,SAAOD,GAAIE,MAAM,GAAGD,EAAAA;AACtB;AAAA,SAqBgBE,GAAQH,IAAAA;AACtB,SAAOA,GACJ1B,QAAQ,qBAAqB,GAAA,EAC7BA,QAAQ,SAAS,GAAA,EACjBA,QAAQ,SAAS,GAAA,EACjBA,QAAQ,eAAe,GAAA,EACvBA,QAAQ,eAAe,GAAA,EACvBA,QAAQ,SAAS,GAAA,EACjBA,QAAQ,mBAAmB,GAAA,EAC3BA,QAAQ,eAAe,GAAA,EACvBA,QAAQ,WAAW,GAAA,EACnBA,QAAQ,iBAAiB,EAAA,EACzBA,QAAQ,OAAO,GAAA,EACftG,YAAAA;AACL;AAEA,SAASoI,GAAuBC,IAAAA;AAC9B,SAAIlF,EAAkBmF,KAAKD,EAAAA,IAClB,UACEpF,EAAmBqF,KAAKD,EAAAA,IAC1B,WACEnF,EAAiBoF,KAAKD,EAAAA,IACxB,SAAA;AAIX;AAEA,SAASE,GACP9C,IACAS,IACAR,IACA8C,IAAAA;AAEA,QAAMC,KAAc/C,GAAMgD;AAE1BhD,EAAAA,GAAMgD,UAAAA;AAEN,MAAIC,KAAwC,CAAC,CAAA,CAAA,GACzCC,KAAM;AAEV,WAASC,KAAAA;AACP,QAAA,CAAKD,GAAK;AAEV,UAAME,KAAOH,GAAMA,GAAM/B,SAAS,CAAA;AAClCkC,IAAAA,GAAKC,KAAKC,MAAMF,IAAM5C,GAAM0C,IAAKlD,EAAAA,CAAAA,GACjCkD,KAAM;EACR;AA4BA,SA1BAnD,GACGwD,KAAAA,EAEAC,MAAM,mBAAA,EACNC,OAAOC,OAAAA,EACPC,QAAQ,CAACC,IAAU5C,IAAG6C,OAAAA;AACG,YAApBD,GAASL,KAAAA,MACXJ,GAAAA,GAEIL,MACQ,MAAN9B,MAAWA,OAAM6C,GAAI3C,SAAS,KAEhC+B,GAAMI,KAAK,CAAA,CAAA,IAOjBH,MAAOU;EAAAA,CAAAA,GAGXT,GAAAA,GAEAnD,GAAMgD,UAAUD,IAETE;AACT;AAoBA,SAASa,GACPrD,IACAD,IACAR,IAAAA;AAMAA,EAAAA,GAAMK,SAAAA;AACN,QAAM0D,KAAQtD,GAAQ,CAAA,IAAqBA,GAAQ,CAAA,EA3B1BG,QAAQtD,GAAkB,EAAA,EAAIkG,MAAM,GAAA,EAE5C1C,IAAI4B,EAAAA,IAyBoC,CAAA,GACnDO,KAAQxC,GAAQ,CAAA,IAvBxB,SACEV,IACAS,IACAR,IAAAA;AAIA,WAFiBD,GAAOwD,KAAAA,EAAOC,MAAM,IAAA,EAErB1C,IAAI,SAAUkD,IAAAA;AAC5B,aAAOnB,GAAcmB,IAASxD,IAAOR,IAAAA,IAAO;IAC9C,CAAA;EACF,EAa6CS,GAAQ,CAAA,GAAID,IAAOR,EAAAA,IAAS,CAAA,GACjEiE,KAASpB,GAAcpC,GAAQ,CAAA,GAAID,IAAOR,IAAAA,CAAAA,CAASiD,GAAM/B,MAAAA;AAG/D,SAFAlB,GAAMK,SAAAA,OAEC4C,GAAM/B,SACT,EACE6C,OAAOA,IACPd,OAAOA,IACPgB,QAAQA,IACRvF,MAAM5G,EAAS0B,MAAAA,IAEjB,EACE0K,UAAUD,IACVvF,MAAM5G,EAASsB,UAAAA;AAEvB;AAEA,SAAS+K,GAAcrC,IAAMsC,IAAAA;AAC3B,SAA+B,QAAxBtC,GAAKiC,MAAMK,EAAAA,IACd,CAAA,IACA,EACEC,WAAWvC,GAAKiC,MAAMK,EAAAA,EAAAA;AAE9B;AA0LA,SAAStE,GAAqDwE,IAAAA;AAG5D,SAFAA,GAAGjE,SAAS,GAELiE;AACT;AAGA,SAASC,GAAYC,IAAAA;AACnB,SAAO1E,GAAY,SAAeC,IAAQC,IAAAA;AACxC,WAAIA,GAAMK,SACDmE,GAAMtE,KAAKH,EAAAA,IAAAA;EAItB,CAAA;AACF;AAGA,SAAS0E,GAAkBD,IAAAA;AACzB,SAAO1E,GAAY,SACjBC,IACAC,IAAAA;AAEA,WAAIA,GAAMK,UAAUL,GAAMM,SACjBkE,GAAMtE,KAAKH,EAAAA,IAAAA;EAItB,CAAA;AACF;AAGA,SAAS2E,GAAWF,IAAAA;AAClB,SAAA,SAAsBzE,IAAgBC,IAAAA;AACpC,WAAIA,GAAMK,UAAUL,GAAMM,SAAAA,OAGjBkE,GAAMtE,KAAKH,EAAAA;EAEtB;AACF;AAGA,SAAS4E,GAAcH,IAAAA;AACrB,SAAO1E,GAAY,SAAeC,IAAAA;AAChC,WAAOyE,GAAMtE,KAAKH,EAAAA;EACpB,CAAA;AACF;AAEA,SAAS6E,GAAe7E,IAAgBC,IAAAA;AACtC,MAAIA,GAAMK,UAAUL,GAAMM,OACxB,QAAA;AAGF,MAAIT,KAAQ;AAEZE,EAAAA,GAAOyD,MAAM,IAAA,EAAMqB,MAAMC,CAAAA,QACvBA,MAAQ,MAAA,CAGJ1C,GAA6B2C,KAAKP,CAAAA,OAASA,GAAM5B,KAAKkC,EAAAA,CAAAA,MAI1DjF,MAASiF,IAAAA,CAAAA,CAEAA,GAAKvB,KAAAA,GAAAA;AAGhB,QAAMyB,KAAWrD,GAAQ9B,EAAAA;AACzB,SAAgB,MAAZmF,KAAAA,OAMG,CAACnF,IAAAA,EAASmF,EAAAA;AACnB;AAAA,SAEgBC,GAAUC,IAAAA;AACxB,MAAA;AAGE,QAFgBC,mBAAmBD,EAAAA,EAAKtE,QAAQ,mBAAmB,EAAA,EAEvDf,MAAM,4CAAA,EAQhB,QAAA;EAcJ,SAZSuF,IAAAA;AAWP,WAAA;EACF;AAEA,SAAOF;AACT;AAEA,SAASG,GAAYC,IAAAA;AACnB,SAAOA,GAAa1E,QAAQvC,IAAgB,IAAA;AAC9C;AAKA,SAASkH,GACP/E,IACA0D,IACAlE,IAAAA;AAEA,QAAMwF,KAAoBxF,GAAMK,UAAAA,OAC1BoF,KAAoBzF,GAAMM,UAAAA;AAChCN,EAAAA,GAAMK,SAAAA,MACNL,GAAMM,SAAAA;AACN,QAAMsB,KAASpB,GAAM0D,IAAUlE,EAAAA;AAG/B,SAFAA,GAAMK,SAASmF,IACfxF,GAAMM,SAASmF,IACR7D;AACT;AAKA,SAAS8D,GACPlF,IACA0D,IACAlE,IAAAA;AAEA,QAAMwF,KAAoBxF,GAAMK,UAAAA,OAC1BoF,KAAoBzF,GAAMM,UAAAA;AAChCN,EAAAA,GAAMK,SAAAA,OACNL,GAAMM,SAAAA;AACN,QAAMsB,KAASpB,GAAM0D,IAAUlE,EAAAA;AAG/B,SAFAA,GAAMK,SAASmF,IACfxF,GAAMM,SAASmF,IACR7D;AACT;AAEA,SAAS+D,GACPnF,IACA0D,IACAlE,IAAAA;AAEA,QAAMwF,KAAoBxF,GAAMK,UAAAA;AAChCL,EAAAA,GAAMK,SAAAA;AACN,QAAMuB,KAASpB,GAAM0D,IAAUlE,EAAAA;AAE/B,SADAA,GAAMK,SAASmF,IACR5D;AACT;AAEA,IAAMgE,KAEDA,CAACnF,IAASD,IAAOR,QACb,EACLkE,UAAUqB,GAAY/E,IAAOC,GAAQ,CAAA,GAAIT,EAAAA,EAAAA;AAI7C,SAAS6F,KAAAA;AACP,SAAO,CAAA;AACT;AAEA,SAASC,KAAAA;AACP,SAAA;AACF;AAwDA,SAASC,MAAMC,IAAAA;AACb,SAAOA,GAAKvC,OAAOC,OAAAA,EAASuC,KAAK,GAAA;AACnC;AAEA,SAASC,GAAIC,IAAaC,IAAcC,IAAAA;AACtC,MAAIC,KAAMH;AACV,QAAMI,KAAQH,GAAK5C,MAAM,GAAA;AAEzB,SAAO+C,GAAMrF,WACXoF,KAAMA,GAAIC,GAAM,CAAA,CAAA,GAAA,WAEZD,MACCC,CAAAA,GAAMC,MAAAA;AAGb,SAAOF,MAAOD;AAChB;AAAA,SAagBI,GACdC,KAAmB,IACnBC,KAAiC,CAAA,GAAA;AAajC,WAASnH,GAEPoH,IACAC,OAIG3C,IAAAA;AAEH,UAAM4C,KAAgBZ,GAAIS,GAAQI,WAAAA,GAAcH,EAAAA,UAAa,CAAA,CAAA;AAE7D,WAAOD,GAAQK,cArCnB,SAAgBJ,IAAaG,IAAAA;AAC3B,YAAME,KAAWf,GAAIa,IAAWH,EAAAA;AAEhC,aAAKK,KAEsB,cAAA,OAAbA,MACS,YAAA,OAAbA,MAAyB,YAAYA,KAC3CA,KACAf,GAAIa,IAAAA,GAAcH,EAAAA,cAAiBA,EAAAA,IALjBA;IAMxB,EA6BaA,IAAKD,GAAQI,SAAAA,GAAUG,EAAAA,CAAAA,GAEzBL,IACAC,IAAAA,EACHK,WAAWpB,GAAAA,QAAGc,KAAAA,SAAAA,GAAOM,WAAWL,GAAcK,SAAAA,KAAAA,OAAclF,CAAAA,GAAAA,GAE3DiC,EAAAA;EAEP;AAEA,WAASkD,GAAQC,IAAAA;AACfA,IAAAA,KAAQA,GAAMzG,QAAQ3E,GAAgB,EAAA;AAEtC,QAAIoE,KAAAA;AAEAsG,IAAAA,GAAQW,cACVjH,KAAAA,OACUsG,GAAQY,eAKlBlH,KAAAA,UAASjD,EAAyBwF,KAAKyE,EAAAA;AAGzC,UAAMxD,KAAM2D,IACVC,IACEpH,KACIgH,KAAAA,GACG1F,GAAQ0F,EAAAA,EAAOzG,QAAQzC,IAAwB,EAAA,CAAA;;GACtD,EACEkC,QAAAA,GAAAA,CAAAA,CAAAA;AAKN,WACiC,YAAA,OAAxBwD,GAAIA,GAAI3C,SAAS,CAAA,KAAA,CACvB2C,GAAIA,GAAI3C,SAAS,CAAA,EAAGqC,KAAAA,IAErBM,CAAAA,GAAI6D,IAAAA;AAGN,QAAwB,SAApBf,GAAQgB,QACV,QAAO9D;AAGT,UAAM8D,KAAUhB,GAAQgB,YAAYtH,KAAS,SAAS;AACtD,QAAIuH;AAEJ,QAAI/D,GAAI3C,SAAS,KAAKyF,GAAQkB,aAC5BD,CAAAA,KAAM/D;SAAAA;AAAAA,UACkB,MAAfA,GAAI3C,OAIb,QAHA0G,KAAM/D,GAAI,CAAA,GAGS,YAAA,OAAR+D,KACFpI,GAAAA,QAAAA,EAAMwC,KAAI,QAAA,GAAS4F,EAAAA,IAEnBA;AAITA,MAAAA,KAAM;IACR;AAEA,WAAOjB,GAAQK,cACbW,IACA,EAAE3F,KAAK,QAAA,GACP4F,EAAAA;EAEJ;AAEA,WAASE,GACPlB,IACAtE,IAAAA;AAEA,UAAMyF,KAAazF,GAAIzC,MAAM5E,CAAAA;AAC7B,WAAK8M,KAIEA,GAAW5N,OAAO,SAAU2G,IAAKkH,IAAAA;AACtC,YAAMC,KAAeD,GAAIzG,QAAQ,GAAA;AAEjC,UAAA,OAAI0G,IAAqB;AACvB,cAAMjG,KA7iBd,SAA+BA,IAAAA;AAS7B,iBAAA,OARoBA,GAAIT,QAAQ,GAAA,KAE4B,SAAlCS,GAAInC,MAAMpD,CAAAA,MAClCuF,KAAMA,GAAIpB,QAAQ7D,GAA6B,SAAUmL,IAAGC,IAAAA;AAC1D,mBAAOA,GAAOC,YAAAA;UAChB,CAAA,IAGKpG;QACT,EAmiB0CgG,GAAIxF,MAAM,GAAGyF,EAAAA,CAAAA,EAAe1E,KAAAA,GACxD8E,KAjsBd,SAAiB/F,IAAAA;AACf,gBAAMgG,KAAQhG,GAAI,CAAA;AAClB,kBACa,QAAVgG,MAA2B,QAAVA,OAClBhG,GAAIpB,UAAU,KACdoB,GAAIA,GAAIpB,SAAS,CAAA,MAAOoH,KAEjBhG,GAAIE,MAAM,GAAA,EAAI,IAEhBF;QACT,EAurB8B0F,GAAIxF,MAAMyF,KAAe,CAAA,EAAG1E,KAAAA,CAAAA,GAE5CgF,KAAYrO,EAA0B8H,EAAAA,KAAQA;AAGpD,YAAkB,UAAduG,GAAqB,QAAOzH;AAEhC,cAAM0H,KAAmB1H,GAAIyH,EAAAA,IAziBrC,SACE3B,IACA5E,IACAqG,IACAI,IAAAA;AAEA,iBAAY,YAARzG,KACKqG,GAAM7E,MAAM,MAAA,EAAQrJ,OAAO,SAAUuO,IAAQC,IAAAA;AAClD,kBAAM3G,KAAM2G,GAAOnG,MAAM,GAAGmG,GAAOpH,QAAQ,GAAA,CAAA;AAW3C,mBAFAmH,GALsB1G,GACnBuB,KAAAA,EACA3C,QAAQ,aAAagI,CAAAA,OAAUA,GAAO,CAAA,EAAGR,YAAAA,CAAAA,CAAAA,IAGpBO,GAAOnG,MAAMR,GAAId,SAAS,CAAA,EAAGqC,KAAAA,GAE9CmF;UACT,GAAG,CAAA,CAAA,IACc,WAAR1G,MAA0B,UAARA,KACpByG,GAAcJ,IAAOzB,IAAK5E,EAAAA,KACxBqG,GAAMxI,MAAMlD,CAAAA,MAErB0L,KAAQA,GAAM7F,MAAM,GAAG6F,GAAMnH,SAAS,CAAA,IAG1B,WAAVmH,MAEiB,YAAVA,MAIJA;QACT,EAugBUzB,IACA5E,IACAqG,IACA1B,GAAQ1B,SAAAA;AAImB,oBAAA,OAApBuD,OACNlM,EAAqBsG,KAAK4F,EAAAA,KACzB9L,EAA4BkG,KAAK4F,EAAAA,OAEnC1H,GAAIyH,EAAAA,IAAanB,GAAQoB,GAAgBjF,KAAAA,CAAAA;MAE7C,MAAmB,aAARyE,OACTlH,GAAI5G,EAA0B8N,EAAAA,KAAQA,EAAAA,IAAAA;AAGxC,aAAOlH;IACT,GAAG,CAAA,CAAA,IAAA;EACL;AAzIA6F,EAAAA,GAAQI,YAAYJ,GAAQI,aAAa,CAAA,GACzCJ,GAAQ1B,YAAY0B,GAAQ1B,aAAaA,IACzC0B,GAAQlE,UAAUkE,GAAQlE,WAAWA,IACrCkE,GAAQlM,sBAAsBkM,GAAQlM,sBAAmByM,EAAAA,CAAAA,GAChDzM,GAAwBkM,GAAQlM,mBAAAA,IACrCA,GAEJkM,GAAQK,gBAAgBL,GAAQK,iBAAuBA;AAwJvD,QAAM6B,KAAwD,CAAA,GACxDC,KAA6D,CAAA,GAQ7DC,KAA6B,EACjC,CAACjR,EAASC,UAAAA,GAAa,EACrB8H,OAAO6E,GAAWtJ,CAAAA,GAClBmF,OAAAA,GACAC,MAAMC,IAASD,IAAOR,IAAAA;AACpB,UAAA,CAAA,EAASgJ,IAAO5H,EAAAA,IAAWX,GAAQ,CAAA,EAChCG,QAAQvF,GAAkC,EAAA,EAC1CwE,MAAMvE,CAAAA;AAET,WAAO,EACL0N,OAAAA,IACA9E,UAAU1D,GAAMY,IAASpB,EAAAA,EAAAA;EAE7B,GACA6B,OAAOC,IAAMC,IAAQ/B,IAAAA;AACnB,UAAM6G,KAAQ,EACZ7E,KAAKhC,GAAMgC,IAAAA;AAiBb,WAdIF,GAAKkH,UACPnC,GAAMM,YACJ,oBACAR,GAAQlE,QAAQX,GAAKkH,MAAM1O,YAAAA,GAAemI,EAAAA,GAE5CX,GAAKoC,SAAS+E,QAAQ,EACpBC,OAAO,CAAA,GACPhF,UAAU,CAAC,EAAExF,MAAM5G,EAAS4B,MAAMA,MAAMoI,GAAKkH,MAAAA,CAAAA,GAC7CG,cAAAA,MACAzK,MAAM5G,EAASY,WACfkO,KAAK,SAAA,CAAA,IAIFpH,GAAE,cAAcqH,IAAO9E,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA;EACtD,EAAA,GAGF,CAAClI,EAASE,SAAAA,GAAY,EACpB6H,OAAO8E,GAAcpJ,CAAAA,GACrBgF,OAAAA,GACAC,OAAOqF,IACPhE,QAAMA,CAACqG,IAAGkB,IAAIpJ,OACLR,GAAAA,MAAAA,EAAIwC,KAAKhC,GAAMgC,IAAAA,CAAAA,EAAAA,GAI1B,CAAClK,EAASG,aAAAA,GAAgB,EACxB4H,OAAO6E,GAAWlJ,CAAAA,GAClB+E,OAAAA,GACAC,OAAOqF,IACPhE,QAAMA,CAACqG,IAAGkB,IAAIpJ,OACLR,GAAAA,MAAAA,EAAIwC,KAAKhC,GAAMgC,IAAAA,CAAAA,EAAAA,GAI1B,CAAClK,EAASI,SAAAA,GAAY,EACpB2H,OAAO6E,GAAWhJ,CAAAA,GAClB6E,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL4I,MAAAA,QACA3P,MAAMiI,GAAQlB,GAAQ,CAAA,EAAGG,QAAQ,WAAW,EAAA,CAAA,EAAKA,QAC/C3C,IACA,IAAA,EAAA,IAKN4D,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,OAAAA,EAAKwC,KAAKhC,GAAMgC,IAAAA,GACdxC,GAAAA,QAAAA,EAAAA,CAAAA,GACMsC,GAAKoH,OAAAA,EACT/B,WAAWrF,GAAKuH,OAAAA,QAAevH,GAAKuH,IAAAA,KAAS,GAAA,CAAA,GAE5CvH,GAAKpI,IAAAA,CAAAA,EAAAA,GAWhB,CAAC5B,EAASK,UAAAA,GAAa,EACrB0H,OAAO6E,GAAWjJ,CAAAA,GAClB8E,OAAAA,GACAC,OAAMC,CAAAA,QACG,EAELyI,OAAOpB,GAAgB,QAAQrH,GAAQ,CAAA,KAAM,EAAA,GAC7C4I,MAAM5I,GAAQ,CAAA,KAAA,QACd/G,MAAM+G,GAAQ,CAAA,EAAGG,QAAQ3C,IAAiB,IAAA,GAC1CS,MAAM5G,EAASI,UAAAA,GAAAA,GAKrB,CAACJ,EAASM,UAAAA,GAAa,EACrByH,OAAO4E,GAAkB9I,CAAAA,GACzB4E,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL/G,MAAM+G,GAAQ,CAAA,EAAGG,QAAQ3C,IAAiB,IAAA,EAAA,IAG9C4D,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,QAAAA,EAAMwC,KAAKhC,GAAMgC,IAAAA,GAAMF,GAAKpI,IAAAA,EAAAA,GAOvC,CAAC5B,EAASO,QAAAA,GAAW,EACnBwH,OAAO6E,GAAW5I,CAAAA,GAClByE,OAAAA,GACAC,OAAMC,CAAAA,QACJoI,GAAUxF,KAAK,EACbhL,UAAUoI,GAAQ,CAAA,GAClB6I,YAAY7I,GAAQ,CAAA,EAAA,CAAA,GAGf,CAAA,IAEToB,QAAQiE,GAAAA,GAGV,CAAChO,EAASQ,iBAAAA,GAAoB,EAC5BuH,OAAO0E,GAAYxI,CAAAA,GACnBwE,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL8I,QAAAA,IAAY5C,GAAQlE,QAAQhC,GAAQ,CAAA,GAAIgC,EAAAA,CAAAA,IACxC/I,MAAM+G,GAAQ,CAAA,EAAA,IAGlBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,KAAAA,EAAGwC,KAAKhC,GAAMgC,KAAKwH,MAAM7C,GAAQ1B,UAAUnD,GAAKyH,QAAQ,KAAK,MAAA,EAAA,GAC3D/J,GAAAA,OAAAA,EAAKwC,KAAKhC,GAAMgC,IAAAA,GAAMF,GAAKpI,IAAAA,CAAAA,EAAAA,GAMnC,CAAC5B,EAASS,OAAAA,GAAU,EAClBsH,OAAO0E,GAAYrI,CAAAA,GACnBqE,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACLgJ,WAAwC,QAA7BhJ,GAAQ,CAAA,EAAGnG,YAAAA,EAAAA,IAG1BuH,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,SAAAA,EACEkK,SAAS5H,GAAK2H,WACdzH,KAAKhC,GAAMgC,KACX2H,UAAAA,MACAjL,MAAK,WAAA,CAAA,EAAA,GAMb,CAAC5G,EAASU,OAAAA,GAAU,EAClBqH,OAAO6E,GACLiC,GAAQiD,qBAAqBxN,IAA0BD,CAAAA,GAEzDoE,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EACLkE,UAAUqB,GAAY/E,IAAOC,GAAQ,CAAA,GAAIT,EAAAA,GACzC6J,IAAIlD,GAAQlE,QAAQhC,GAAQ,CAAA,GAAIgC,EAAAA,GAChCqH,OAAOrJ,GAAQ,CAAA,EAAGS,OAAAA,IAGtBW,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,IACDsC,GAAKgI,KAAAA,IACT,EAAED,IAAI/H,GAAK+H,IAAI7H,KAAKhC,GAAMgC,IAAAA,GAC1BD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAK5B,CAAClI,EAASW,aAAAA,GAAgB,EACxBoH,OAAO6E,GAAWrI,CAAAA,GAClBkE,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EACLkE,UAAUqB,GAAY/E,IAAOC,GAAQ,CAAA,GAAIT,EAAAA,GACzC8J,OAAsB,QAAfrJ,GAAQ,CAAA,IAAa,IAAI,GAChC/B,MAAM5G,EAASU,QAAAA,GAAAA,GAKrB,CAACV,EAASY,SAAAA,GAAY,EAIpBmH,OAAO8E,GAAcrI,CAAAA,GACrBiE,OAAAA,GACAC,MAAMC,IAASD,IAAOR,IAAAA;AACpB,UAAA,CAAA,EAAS+J,EAAAA,IAActJ,GAAQ,CAAA,EAAGZ,MAAMzB,EAAAA,GAElC4L,KAAU,IAAIpM,OAAAA,IAAWmM,EAAAA,IAAc,IAAA,GACvCE,KAAUxJ,GAAQ,CAAA,EAAGG,QAAQoJ,IAAS,EAAA,GAEtCE,MAr9Be7C,KAq9BiB4C,IAp9BrC5H,GAAe0C,KAAKoF,CAAAA,OAAKA,GAAEvH,KAAKyE,EAAAA,CAAAA,IAq9B7B1B,KACAJ;AAv9BZ,QAA6B8B;AAy9BrB,UAAM+C,KAAU3J,GAAQ,CAAA,EAAGnG,YAAAA,GACrB6O,KAAAA,OACJnO,EAA6BuG,QAAQ6I,EAAAA,GAEjCxD,MACJuC,KAAeiB,KAAU3J,GAAQ,CAAA,GACjC8C,KAAAA,GAEI8G,KAAM,EACVnB,OAAOpB,GAAgBlB,IAAKnG,GAAQ,CAAA,CAAA,GACpC0I,cAAcA,IACdvC,KAAAA,GAAAA;AAuBF,WAdA5G,GAAMsK,WAAWtK,GAAMsK,YAAwB,QAAZF,IAE/BjB,KACFkB,GAAI3Q,OAAO+G,GAAQ,CAAA,IAEnB4J,GAAInG,WAAWgG,GAAU1J,IAAOyJ,IAASjK,EAAAA,GAO3CA,GAAMsK,WAAAA,OAECD;EACT,GACAxI,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAACsC,GAAK8E,KAAGM,EAAAA,EAAClF,KAAKhC,GAAMgC,IAAAA,GAASF,GAAKoH,KAAAA,GAChCpH,GAAKpI,SAASoI,GAAKoC,WAAWnC,GAAOD,GAAKoC,UAAUlE,EAAAA,IAAS,GAAA,EAAA,GAMtE,CAAClI,EAASc,eAAAA,GAAkB,EAI1BiH,OAAO8E,GAAcjI,CAAAA,GACrB6D,OAAAA,GACAC,MAAMC,IAAAA;AACJ,UAAMmG,KAAMnG,GAAQ,CAAA,EAAG8C,KAAAA;AAEvB,WAAO,EACL2F,OAAOpB,GAAgBlB,IAAKnG,GAAQ,CAAA,KAAM,EAAA,GAC1CmG,KAAAA,GAAAA;EAEJ,GACA/E,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAACsC,GAAK8E,KAAGM,EAAAA,CAAAA,GAAKpF,GAAKoH,OAAAA,EAAOlH,KAAKhC,GAAMgC,IAAAA,CAAAA,CAAAA,EAAAA,GAIhD,CAAClK,EAASa,WAAAA,GAAc,EACtBkH,OAAO8E,GAAcnI,CAAAA,GACrB+D,OAAAA,GACAC,OAAKA,OACI,CAAA,IAETqB,QAAQiE,GAAAA,GAGV,CAAChO,EAASe,KAAAA,GAAQ,EAChBgH,OAAO4E,GAAkBtC,EAAAA,GACzB5B,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL8J,KAAK9J,GAAQ,CAAA,GACb8I,QAAQlE,GAAY5E,GAAQ,CAAA,CAAA,GAC5B+J,OAAO/J,GAAQ,CAAA,EAAA,IAGnBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,OAAAA,EACEwC,KAAKhC,GAAMgC,KACXuI,KAAKzI,GAAKyI,OAAAA,QACVC,OAAO1I,GAAK0I,SAAAA,QACZrE,KAAKQ,GAAQ1B,UAAUnD,GAAKyH,QAAQ,OAAO,KAAA,EAAA,CAAA,EAAA,GAUnD,CAACzR,EAASgB,IAAAA,GAAO,EACf+G,OAAO0E,GAAYrC,EAAAA,GACnB3B,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EACLkE,UAAUwB,GAAkBlF,IAAOC,GAAQ,CAAA,GAAIT,EAAAA,GAC/CuJ,QAAQlE,GAAY5E,GAAQ,CAAA,CAAA,GAC5B+J,OAAO/J,GAAQ,CAAA,EAAA,IAGnBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OAEjBR,GAAAA,KAAAA,EACEwC,KAAKhC,GAAMgC,KACXwH,MAAM7C,GAAQ1B,UAAUnD,GAAKyH,QAAQ,KAAK,MAAA,GAC1CiB,OAAO1I,GAAK0I,MAAAA,GAEXzI,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAO/B,CAAClI,EAASiB,2BAAAA,GAA8B,EACtC8G,OAAO0E,GAAYzH,CAAAA,GACnByD,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACLyD,UAAU,CACR,EACExK,MAAM+G,GAAQ,CAAA,GACd/B,MAAM5G,EAAS4B,KAAAA,CAAAA,GAGnB6P,QAAQ9I,GAAQ,CAAA,GAChB/B,MAAM5G,EAASgB,KAAAA,GAAAA,GAKrB,CAAChB,EAASkB,mBAAAA,GAAsB,EAC9B6G,OAAOC,GAAY,CAACC,IAAQC,OACtBA,GAAMsK,YAAY3D,GAAQ8D,kBAAAA,OAIvBlG,GAAY3H,CAAAA,EAA0BmD,IAAQC,EAAAA,CAAAA,GAEvDO,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACLyD,UAAU,CACR,EACExK,MAAM+G,GAAQ,CAAA,GACd/B,MAAM5G,EAAS4B,KAAAA,CAAAA,GAGnB6P,QAAQ9I,GAAQ,CAAA,GAChB+J,OAAAA,QACA9L,MAAM5G,EAASgB,KAAAA,GAAAA,GAKrB,CAAChB,EAASmB,kBAAAA,GAAqB,EAC7B4G,OAAO0E,GAAY1H,CAAAA,GACnB0D,OAAAA,GACAC,MAAMC,IAAAA;AACJ,QAAIiK,KAAUjK,GAAQ,CAAA,GAClB8I,KAAS9I,GAAQ,CAAA;AAOrB,WAJKvF,EAAwB0H,KAAK2G,EAAAA,MAChCA,KAAS,YAAYA,KAGhB,EACLrF,UAAU,CACR,EACExK,MAAMgR,GAAQ9J,QAAQ,WAAW,EAAA,GACjClC,MAAM5G,EAAS4B,KAAAA,CAAAA,GAGnB6P,QAAQA,IACR7K,MAAM5G,EAASgB,KAAAA;EAEnB,EAAA,GAGF,CAAChB,EAASqB,WAAAA,GAAcoG,GACtBC,IA74CqB,CAAA,GAi5CvB,CAAC1H,EAASkC,aAAAA,GAAgBuF,GACxBC,IAj5CuB,CAAA,GAq5CzB,CAAC1H,EAASoB,gBAAAA,GAAmB,EAC3B2G,OAAO6E,GAAW9I,CAAAA,GAClB2E,OAAAA,GACAC,OAAOqF,IACPhE,QAAMA,MACG,KAAA,GAIX,CAAC/J,EAASsB,SAAAA,GAAY,EACpByG,OAAOC,GAAY8E,EAAAA,GACnBrE,OAAAA,GACAC,OAAOoF,IACP/D,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,KAAAA,EAAGwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAIrD,CAAClI,EAASuB,GAAAA,GAAM,EACdwG,OAAO0E,GAAYtH,CAAAA,GACnBsD,OAAAA,GACAC,OAAMC,CAAAA,QACJqI,GAAKrI,GAAQ,CAAA,CAAA,IAAM,EACjB8I,QAAQ9I,GAAQ,CAAA,GAChB+J,OAAO/J,GAAQ,CAAA,EAAA,GAGV,CAAA,IAEToB,QAAQiE,GAAAA,GAGV,CAAChO,EAASwB,QAAAA,GAAW,EACnBuG,OAAO4E,GAAkBvH,CAAAA,GACzBqD,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL8J,KAAK9J,GAAQ,CAAA,KAAA,QACbpH,KAAKoH,GAAQ,CAAA,EAAA,IAGjBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OACZ8I,GAAKhH,GAAKzI,GAAAA,IACfmG,GAAAA,OAAAA,EACEwC,KAAKhC,GAAMgC,KACXuI,KAAKzI,GAAKyI,KACVpE,KAAKQ,GAAQ1B,UAAU6D,GAAKhH,GAAKzI,GAAAA,EAAKkQ,QAAQ,OAAO,KAAA,GACrDiB,OAAO1B,GAAKhH,GAAKzI,GAAAA,EAAKmR,MAAAA,CAAAA,IAEtB,KAAA,GAIR,CAAC1S,EAASyB,OAAAA,GAAU,EAClBsG,OAAO0E,GAAYpH,CAAAA,GACnBoD,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EACLkE,UAAU1D,GAAMC,GAAQ,CAAA,GAAIT,EAAAA,GAC5B2K,kBAAkBlK,GAAQ,CAAA,GAC1BpH,KAAKoH,GAAQ,CAAA,EAAA,IAGjBoB,QAAMA,CAACC,IAAMC,IAAQ/B,OACZ8I,GAAKhH,GAAKzI,GAAAA,IACfmG,GAAAA,KAAAA,EACEwC,KAAKhC,GAAMgC,KACXwH,MAAM7C,GAAQ1B,UAAU6D,GAAKhH,GAAKzI,GAAAA,EAAKkQ,QAAQ,KAAK,MAAA,GACpDiB,OAAO1B,GAAKhH,GAAKzI,GAAAA,EAAKmR,MAAAA,GAErBzI,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,IAGzBR,GAAAA,QAAAA,EAAMwC,KAAKhC,GAAMgC,IAAAA,GAAMF,GAAK6I,gBAAAA,EAAAA,GAKlC,CAAC7S,EAAS0B,KAAAA,GAAQ,EAChBqG,OAAO6E,GAAW1H,CAAAA,GAClBuD,OAAAA,GACAC,OAAOsD,IACPjC,OAAOC,IAAMC,IAAQ/B,IAAAA;AACnB,UAAMxG,KAAQsI;AACd,WACEtC,GAAAA,SAAAA,EAAOwC,KAAKhC,GAAMgC,IAAAA,GAChBxC,GAAAA,SAAAA,MACEA,GAAAA,MAAAA,MACGhG,GAAMyK,OAAOnD,IAAI,SAA4BM,IAASJ,IAAAA;AACrD,aACExB,GAAAA,MAAAA,EAAIwC,KAAKhB,IAAG4J,OAAOzG,GAAc3K,IAAOwH,EAAAA,EAAAA,GACrCe,GAAOX,IAASpB,EAAAA,CAAAA;IAGvB,CAAA,CAAA,CAAA,GAIJR,GAAAA,SAAAA,MACGhG,GAAMyJ,MAAMnC,IAAI,SAA0B+J,IAAK7J,IAAAA;AAC9C,aACExB,GAAAA,MAAAA,EAAIwC,KAAKhB,GAAAA,GACN6J,GAAI/J,IAAI,SAA2BM,IAAS0J,IAAAA;AAC3C,eACEtL,GAAAA,MAAAA,EAAIwC,KAAK8I,IAAGF,OAAOzG,GAAc3K,IAAOsR,EAAAA,EAAAA,GACrC/I,GAAOX,IAASpB,EAAAA,CAAAA;MAGvB,CAAA,CAAA;IAGN,CAAA,CAAA,CAAA;EAIR,EAAA,GAGF,CAAClI,EAAS4B,IAAAA,GAAO,EAKfmG,OAAO8E,GAAczG,EAAAA,GACrBqC,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL/G,MAAM+G,GAAQ,CAAA,EAEXG,QAAQrE,GAAkB,CAACwO,IAAMC,OACzBrE,GAAQlM,oBAAoBuQ,EAAAA,IAC/BrE,GAAQlM,oBAAoBuQ,EAAAA,IAC5BD,EAAAA,EAAAA,IAIZlJ,QAAOC,CAAAA,OACEA,GAAKpI,KAAAA,GAIhB,CAAC5B,EAAS6B,UAAAA,GAAa,EACrBkG,OAAO4E,GAAkB9G,CAAAA,GACzB4C,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EAGLkE,UAAU1D,GAAMC,GAAQ,CAAA,GAAIT,EAAAA,EAAAA,IAGhC6B,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,UAAAA,EAAQwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAI1D,CAAClI,EAAS8B,cAAAA,GAAiB,EACzBiG,OAAO4E,GAAkB5G,CAAAA,GACzB0C,OAAAA,GACAC,OAAKA,CAACC,IAASD,IAAOR,QACb,EAGLkE,UAAU1D,GAAMC,GAAQ,CAAA,GAAIT,EAAAA,EAAAA,IAGhC6B,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,MAAAA,EAAIwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAItD,CAAClI,EAAS+B,WAAAA,GAAc,EAKtBgG,OAAO4E,GAAkBzG,EAAAA,GACzBuC,OAAAA,GACAC,OAAMC,CAAAA,QACG,EACL/G,MAAM+G,GAAQ,CAAA,GACd/B,MAAM5G,EAAS4B,KAAAA,GAAAA,GAKrB,CAAC5B,EAASgC,UAAAA,GAAa,EACrB+F,OAAO4E,GAAkB3G,CAAAA,GACzByC,OAAAA,GACAC,OAAOoF,IACP/D,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,QAAAA,EAAMwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,GAIxD,CAAClI,EAASiC,mBAAAA,GAAsB,EAC9B8F,OAAO4E,GAAkB1G,CAAAA,GACzBwC,OAAAA,GACAC,OAAOoF,IACP/D,QAAMA,CAACC,IAAMC,IAAQ/B,OACZR,GAAAA,OAAAA,EAAKwC,KAAKhC,GAAMgC,IAAAA,GAAMD,GAAOD,GAAKoC,UAAUlE,EAAAA,CAAAA,EAAAA,EAAAA;AAAAA,WAoCrD2G,GAAQsE,0BAAAA,OACHlC,GAAMjR,EAASY,SAAAA,GAAAA,OACfqQ,GAAMjR,EAASc,eAAAA;AAGxB,QAAM6O,MA9qCR,SACEsB,IAAAA;AAOA,QAAImC,KAAWC,OAAOC,KAAKrC,EAAAA;AA8B3B,aAASsC,GACPtL,IACAC,IAAAA;AAEA,UACIsL,IAEAC,IAHA3J,KAAS,CAAA,GAET4J,KAAW,IAEXC,KAAoB;AAQxB,WANAzL,GAAMG,cAAcH,GAAMG,eAAe,IAMlCJ,MAAQ;AACb,YAAIiB,KAAI;AACR,eAAOA,KAAIkK,GAAShK,UAAQ;AAI1B,cAHAsK,KAAWN,GAASlK,EAAAA,GACpBsK,KAAOvC,GAAMyC,EAAAA,GAETxL,GAAMK,UAAAA,CAAWiL,GAAKzL,MAAMQ,QAAQ;AACtCW,YAAAA;AACA;UACF;AAEA,gBAAMP,KAAU6K,GAAKzL,MAAME,IAAQC,EAAAA;AAEnC,cAAIS,IAAS;AACXgL,YAAAA,KAAoBhL,GAAQ,CAAA,GAG5BT,GAAMG,eAAesL,IAErB1L,KAASA,GAAO2L,UAAUD,GAAkBvK,MAAAA,GAE5CqK,KAASD,GAAK9K,MAAMC,IAAS4K,IAAarL,EAAAA,GAMvB,QAAfuL,GAAO7M,SACT6M,GAAO7M,OAAO8M,KAGhB5J,GAAOyB,KAAKkI,EAAAA;AACZ;UACF;AAEAvK,UAAAA;QACF;MACF;AAKA,aAFAhB,GAAMG,cAAc,IAEbyB;IACT;AAEA,WA3EAsJ,GAASS,KAAK,SAAUC,IAAOC,IAAAA;AAC7B,UAAIC,KAAS/C,GAAM6C,EAAAA,EAAOrL,OACtBwL,KAAShD,GAAM8C,EAAAA,EAAOtL;AAG1B,aAAIuL,OAAWC,KACND,KAASC,KACPH,KAAQC,KAAAA,KACT;IAIZ,CAAA,GAAA,SA+D2B9L,IAAQC,IAAAA;AACjC,aAAOqL,GA9HX,SAA6BtL,IAAAA;AAC3B,eAAOA,GACJa,QAAQ/E,GAAc,IAAA,EACtB+E,QAAQ5E,GAAY,EAAA,EACpB4E,QAAQvD,GAAO,MAAA;MACpB,EAyH2C0C,EAAAA,GAASC,EAAAA;IAClD;EACF,EAwkC2B+I,EAAAA,GACnBvB,OAj5BU3F,MAqClB,yBACEkH,IACAiD,IAAAA;AAEA,WAAA,SACE3B,IACAxI,IACA7B,IAAAA;AAEA,YAAMiM,KAAWlD,GAAMsB,GAAI3L,IAAAA,EAAMmD;AAEjC,aAAOmK,KACHA,GAAW,MAAMC,GAAS5B,IAAKxI,IAAQ7B,EAAAA,GAAQqK,IAAKxI,IAAQ7B,EAAAA,IAC5DiM,GAAS5B,IAAKxI,IAAQ7B,EAAAA;IAC5B;EACF,EA61BoD+I,IAAOpC,GAAQuF,UAAAA,GAAAA,SAh5BjDC,GACd9B,IACArK,KAA6B,CAAA,GAAA;AAE7B,QAAIoM,MAAMC,QAAQhC,EAAAA,GAAM;AACtB,YAAMiC,KAAStM,GAAMgC,KACfJ,KAAS,CAAA;AAIf,UAAI2K,KAAAA;AAEJ,eAASvL,KAAI,GAAGA,KAAIqJ,GAAInJ,QAAQF,MAAK;AACnChB,QAAAA,GAAMgC,MAAMhB;AAEZ,cAAMwL,KAAUL,GAAc9B,GAAIrJ,EAAAA,GAAIhB,EAAAA,GAChCyM,KAA8B,YAAA,OAAZD;AAEpBC,QAAAA,MAAYF,KACd3K,GAAOA,GAAOV,SAAS,CAAA,KAAMsL,KACR,SAAZA,MACT5K,GAAOyB,KAAKmJ,EAAAA,GAGdD,KAAgBE;MAClB;AAIA,aAFAzM,GAAMgC,MAAMsK,IAEL1K;IACT;AAEA,WAAOC,IAAOwI,IAAK8B,IAAenM,EAAAA;EACpC;AAlCF,MAAkB6B;AAm5BhB,QAAM+F,MAAMR,GAAQV,EAAAA;AAEpB,SAAImC,GAAU3H,SAEV1B,GAAAA,OAAAA,MACGoI,KACDpI,GAAAA,UAAAA,EAAQwC,KAAI,SAAA,GACT6G,GAAU/H,IAAI,SAAwB4L,IAAAA;AACrC,WACElN,GAAAA,OAAAA,EACEqK,IAAIlD,GAAQlE,QAAQiK,GAAIpD,YAAY7G,EAAAA,GACpCT,KAAK0K,GAAIpD,WAAAA,GAERoD,GAAIpD,YACJ9B,IAAQC,IAAOiF,GAAIrU,UAAU,EAAEgI,QAAAA,KAAQ,CAAA,CAAA,CAAA;EAG9C,CAAA,CAAA,CAAA,IAMDuH;AACT;AAAA,IAAA,uBAWI+E,CAAAA,OAAAA;AAAAA,MAAAA,EAACzI,UAAEA,KAAW,IAAEyC,SAAEA,GAAAA,IAAmBgG,IAAP9F,KAAAA,SAAAA,IAAAA,IAAAA;AAAAA,QAAAA,QAAAA,GAAAA,QAAAA,CAAAA;AAAAA,QAAAA,IAAAA,IAAAA,KAAAA,CAAAA,GAAAA,KAAAA,OAAAA,KAAAA,EAAAA;AAAAA,SAAAA,KAAAA,GAAAA,KAAAA,GAAAA,QAAAA,KAAAA,CAAAA,GAAAA,QAAAA,KAAAA,GAAAA,EAAAA,CAAAA,KAAAA,MAAAA,GAAAA,EAAAA,IAAAA,GAAAA,EAAAA;AAAAA,WAAAA;EAAAA,EAAK8F,IAAAC,CAAAA;AAQrC,SAAaC,eACXpG,GAASvC,IAAUyC,EAAAA,GACnBE,EAAAA;AAAsC;", "names": ["RuleType", "blockQuote", "breakLine", "breakThematic", "codeBlock", "codeFenced", "codeInline", "footnote", "footnoteReference", "gfmTask", "heading", "headingSetext", "htmlBlock", "htmlComment", "htmlSelfClosing", "image", "link", "linkAngleBraceStyleDetector", "linkBareUrlDetector", "linkMailtoDetector", "new<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderedList", "paragraph", "ref", "refImage", "refLink", "table", "tableSeparator", "text", "textBolded", "textEmphasized", "textEscaped", "textMarked", "textStrikethroughed", "unorderedList", "Priority", "ATTRIBUTE_TO_JSX_PROP_MAP", "reduce", "obj", "x", "toLowerCase", "class", "for", "namedCodesToUnicode", "amp", "apos", "gt", "lt", "nbsp", "quot", "DO_NOT_PROCESS_HTML_ELEMENTS", "ATTR_EXTRACTOR_R", "AUTOLINK_MAILTO_CHECK_R", "BLOCK_END_R", "BLOCKQUOTE_R", "BLOCKQUOTE_TRIM_LEFT_MULTILINE_R", "BLOCKQUOTE_ALERT_R", "BREAK_LINE_R", "BREAK_THEMATIC_R", "CODE_BLOCK_FENCED_R", "CODE_BLOCK_R", "CODE_INLINE_R", "CONSECUTIVE_NEWLINE_R", "CR_NEWLINE_R", "FOOTNOTE_R", "FOOTNOTE_REFERENCE_R", "FORMFEED_R", "FRONT_MATTER_R", "GFM_TASK_R", "HEADING_R", "HEADING_ATX_COMPLIANT_R", "HEADING_SETEXT_R", "HTML_BLOCK_ELEMENT_R", "HTML_CHAR_CODE_R", "HTML_COMMENT_R", "HTML_CUSTOM_ATTR_R", "HTML_SELF_CLOSING_ELEMENT_R", "INTERPOLATION_R", "LINK_AUTOLINK_BARE_URL_R", "LINK_AUTOLINK_MAILTO_R", "LINK_AUTOLINK_R", "CAPTURE_LETTER_AFTER_HYPHEN", "NP_TABLE_R", "REFERENCE_IMAGE_OR_LINK", "REFERENCE_IMAGE_R", "REFERENCE_LINK_R", "SHOULD_RENDER_AS_BLOCK_R", "TAB_R", "TABLE_TRIM_PIPES", "TABLE_CENTER_ALIGN", "TABLE_LEFT_ALIGN", "TABLE_RIGHT_ALIGN", "INLINE_SKIP_R", "TEXT_BOLD_R", "RegExp", "TEXT_EMPHASIZED_R", "TEXT_MARKED_R", "TEXT_STRIKETHROUGHED_R", "TEXT_ESCAPED_R", "TEXT_UNESCAPE_R", "TEXT_PLAIN_R", "TRIM_STARTING_NEWLINES", "HTML_LEFT_TRIM_AMOUNT_R", "UNESCAPE_URL_R", "LIST_LOOKBEHIND_R", "ORDERED_LIST_BULLET", "UNORDERED_LIST_BULLET", "generateListItemPrefix", "type", "ORDERED_LIST_ITEM_PREFIX", "UNORDERED_LIST_ITEM_PREFIX", "generateListItemPrefixRegex", "ORDERED_LIST_ITEM_PREFIX_R", "UNORDERED_LIST_ITEM_PREFIX_R", "generateListItemRegex", "ORDERED_LIST_ITEM_R", "UNORDERED_LIST_ITEM_R", "generateListRegex", "bullet", "ORDERED_LIST_R", "UNORDERED_LIST_R", "generateListRule", "h", "ordered", "LIST_R", "LIST_ITEM_R", "LIST_ITEM_PREFIX_R", "match", "allowInline", "source", "state", "isStartOfLine", "exec", "prevCapture", "list", "inline", "simple", "order", "parse", "capture", "start", "items", "replace", "lastItemWasAParagraph", "map", "item", "i", "space", "length", "spaceRegex", "content", "isLastItem", "thisItemIsAParagraph", "indexOf", "oldStateInline", "oldStateList", "adjustedContent", "trimEnd", "result", "render", "node", "output", "key", "undefined", "LINK_R", "IMAGE_R", "NON_PARAGRAPH_BLOCK_SYNTAXES", "BLOCK_SYNTAXES", "str", "end", "slice", "slugify", "parseTableAlignCapture", "alignCapture", "test", "parseTableRow", "tableOutput", "prevInTable", "inTable", "cells", "acc", "flush", "cell", "push", "apply", "trim", "split", "filter", "Boolean", "for<PERSON>ach", "fragment", "arr", "parseTable", "align", "rowText", "header", "children", "getTableStyle", "colIndex", "textAlign", "fn", "inlineRegex", "regex", "simpleInlineRegex", "blockRegex", "anyScopeRegex", "matchParagraph", "every", "line", "some", "captured", "sanitizer", "url", "decodeURIComponent", "e", "unescapeUrl", "rawUrlString", "parseInline", "isCurrentlyInline", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseSimpleInline", "parseBlock", "parseCaptureInline", "captureNothing", "renderNothing", "cx", "args", "join", "get", "src", "path", "fb", "ptr", "frags", "shift", "compiler", "markdown", "options", "tag", "props", "overrideProps", "overrides", "createElement", "override", "_extends", "className", "compile", "input", "forceInline", "forceBlock", "emitter", "parser", "pop", "wrapper", "jsx", "forceWrapper", "attrStringToMap", "attributes", "raw", "delimiterIdx", "_", "letter", "toUpperCase", "value", "first", "<PERSON><PERSON><PERSON>", "normalizedValue", "sanitizeUrlFn", "styles", "kvPair", "substr", "footnotes", "refs", "rules", "alert", "unshift", "attrs", "noInnerParse", "__", "lang", "identifier", "target", "href", "completed", "checked", "readOnly", "enforceAtxHeadings", "id", "level", "whitespace", "trimmer", "trimmed", "parseFunc", "r", "tagName", "ast", "inAnchor", "alt", "title", "disableAutoLink", "address", "fallback<PERSON><PERSON><PERSON><PERSON>", "style", "row", "c", "full", "inner", "disableParsingRawHTML", "ruleList", "Object", "keys", "nestedParse", "rule", "parsed", "ruleType", "currCaptureString", "substring", "sort", "typeA", "typeB", "orderA", "orderB", "userRender", "renderer", "renderRule", "patchedRender", "Array", "isArray", "<PERSON><PERSON><PERSON>", "lastWasString", "nodeOut", "isString", "def", "_ref", "_excluded", "cloneElement"]}